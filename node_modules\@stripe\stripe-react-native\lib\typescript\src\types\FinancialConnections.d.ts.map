{"version": 3, "file": "FinancialConnections.d.ts", "sourceRoot": "", "sources": ["../../../../src/types/FinancialConnections.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EAAE,kBAAkB,EAAE,MAAM,UAAU,CAAC;AACnD,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,SAAS,CAAC;AAC3C,OAAO,KAAK,EAAE,WAAW,EAAE,MAAM,UAAU,CAAC;AAE5C,MAAM,MAAM,yCAAyC,GAAG;IACtD,4MAA4M;IAC5M,KAAK,CAAC,EAAE,kBAAkB,CAAC;IAC3B,mKAAmK;IACnK,OAAO,CAAC,EAAE,CAAC,KAAK,EAAE,yBAAyB,KAAK,IAAI,CAAC;CACtD,CAAC;AAEF,MAAM,MAAM,aAAa,GACrB;IACE,wDAAwD;IACxD,OAAO,EAAE,OAAO,CAAC;IACjB,KAAK,CAAC,EAAE,SAAS,CAAC;CACnB,GACD;IACE,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB,KAAK,EAAE,WAAW,CAAC,8BAA8B,CAAC,CAAC;CACpD,CAAC;AAEN,MAAM,MAAM,WAAW,GACnB;IACE,wDAAwD;IACxD,OAAO,EAAE,OAAO,CAAC;IACjB,gEAAgE;IAChE,KAAK,EAAE,gBAAgB,CAAC;IACxB,KAAK,CAAC,EAAE,SAAS,CAAC;CACnB,GACD;IACE,OAAO,CAAC,EAAE,SAAS,CAAC;IACpB,KAAK,CAAC,EAAE,SAAS,CAAC;IAClB,KAAK,EAAE,WAAW,CAAC,8BAA8B,CAAC,CAAC;CACpD,CAAC;AAEN,MAAM,MAAM,OAAO,GAAG;IACpB,oCAAoC;IACpC,EAAE,EAAE,MAAM,CAAC;IACX,0CAA0C;IAC1C,YAAY,EAAE,MAAM,CAAC;IACrB,iHAAiH;IACjH,QAAQ,EAAE,OAAO,CAAC;IAClB,gEAAgE;IAChE,QAAQ,EAAE,KAAK,CAAC,OAAO,CAAC,CAAC;CAC1B,CAAC;AAEF,MAAM,MAAM,gBAAgB,GAAG;IAC7B,4BAA4B;IAC5B,WAAW,EAAE,WAAW,GAAG,IAAI,CAAC;IAChC,iHAAiH;IACjH,QAAQ,EAAE,OAAO,CAAC;IAClB,kCAAkC;IAClC,EAAE,EAAE,MAAM,GAAG,IAAI,CAAC;IAClB,IAAI,EAAE,OAAO,CAAC;IACd,IAAI,EAAE,aAAa,CAAC;IACpB,+EAA+E;IAC/E,OAAO,EAAE,MAAM,GAAG,IAAI,CAAC;CACxB,CAAC;AAEF,MAAM,MAAM,OAAO,GAAG;IACpB,0DAA0D;IAC1D,EAAE,EAAE,MAAM,CAAC;IACX,iHAAiH;IACjH,QAAQ,EAAE,OAAO,CAAC;IAClB,WAAW,EAAE,MAAM,GAAG,IAAI,CAAC;IAC3B,mFAAmF;IACnF,MAAM,EAAE,aAAa,CAAC;IACtB,eAAe,EAAE,MAAM,CAAC;IACxB,KAAK,EAAE,MAAM,GAAG,IAAI,CAAC;IACrB,iFAAiF;IACjF,OAAO,EAAE,MAAM,CAAC;IAChB,mCAAmC;IACnC,OAAO,EAAE,OAAO,GAAG,IAAI,CAAC;IACxB,uEAAuE;IACvE,cAAc,EAAE,cAAc,GAAG,IAAI,CAAC;IACtC,+EAA+E;IAC/E,QAAQ,EAAE,QAAQ,CAAC;IACnB,kHAAkH;IAClH,WAAW,EAAE,WAAW,CAAC;IACzB,wEAAwE;IACxE,WAAW,EAAE,KAAK,CAAC,UAAU,CAAC,GAAG,IAAI,CAAC;IACtC,2DAA2D;IAC3D,2BAA2B,EAAE,KAAK,CAAC,iBAAiB,CAAC,CAAC;CACvD,CAAC;AAEF,MAAM,MAAM,aAAa,GAAG,QAAQ,GAAG,UAAU,GAAG,cAAc,CAAC;AAEnE,MAAM,MAAM,QAAQ,GAAG,MAAM,GAAG,QAAQ,GAAG,YAAY,GAAG,OAAO,CAAC;AAElE,MAAM,MAAM,iBAAiB,GAAG,iBAAiB,GAAG,MAAM,CAAC;AAE3D,MAAM,MAAM,WAAW,GACnB,UAAU,GACV,YAAY,GACZ,cAAc,GACd,UAAU,GACV,OAAO,GACP,SAAS,CAAC;AAEd,MAAM,MAAM,UAAU,GAClB,UAAU,GACV,WAAW,GACX,eAAe,GACf,cAAc,GACd,gBAAgB,CAAC;AAErB,MAAM,MAAM,OAAO,GAAG;IACpB,0GAA0G;IAC1G,IAAI,EAAE,MAAM,CAAC;IACb,uDAAuD;IACvD,IAAI,EAAE,WAAW,CAAC;IAClB,sXAAsX;IACtX,IAAI,EAAE;QAAE,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;KAAE,CAAC;IAChD,yUAAyU;IACzU,MAAM,EAAE;QAAE,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,GAAG,IAAI,CAAA;KAAE,CAAC;IAC7C,qUAAqU;IACrU,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;CAC9B,CAAC;AAEF,MAAM,MAAM,cAAc,GAAG;IAC3B,MAAM,EAAE,oBAAoB,CAAC;IAC7B,wGAAwG;IACxG,eAAe,EAAE,MAAM,CAAC;CACzB,CAAC;AAEF,MAAM,MAAM,WAAW,GAAG,MAAM,GAAG,QAAQ,CAAC;AAE5C,MAAM,MAAM,oBAAoB,GAAG,QAAQ,GAAG,SAAS,GAAG,WAAW,CAAC;AAEtE,oBAAY,8BAA8B;IACxC,MAAM,WAAW;IACjB,QAAQ,aAAa;CACtB;AAED,MAAM,MAAM,yBAAyB,GAAG;IACtC,iHAAiH;IACjH,IAAI,EAAE,6BAA6B,CAAC;IACpC,wFAAwF;IACxF,QAAQ,EAAE,iCAAiC,CAAC;CAC7C,CAAC;AAEF,oBAAY,6BAA6B;IACvC,iDAAiD;IACjD,IAAI,SAAS;IACb,uDAAuD;IACvD,oBAAoB,2BAA2B;IAC/C,yEAAyE;IACzE,eAAe,qBAAqB;IACpC,2GAA2G;IAC3G,eAAe,qBAAqB;IACpC,oGAAoG;IACpG,mBAAmB,yBAAyB;IAC5C,gEAAgE;IAChE,qBAAqB,2BAA2B;IAChD,oEAAoE;IACpE,gBAAgB,sBAAsB;IACtC,kHAAkH;IAClH,OAAO,YAAY;IACnB,mFAAmF;IACnF,KAAK,UAAU;IACf,yFAAyF;IACzF,MAAM,WAAW;IACjB,6JAA6J;IAC7J,qBAAqB,6BAA6B;CACnD;AAED,MAAM,MAAM,iCAAiC,GAAG;IAC9C,sGAAsG;IACtG,WAAW,CAAC,EAAE,OAAO,CAAC;IACtB,oFAAoF;IACpF,eAAe,CAAC,EAAE,MAAM,CAAC;IACzB,uEAAuE;IACvE,SAAS,CAAC,EAAE,kCAAkC,CAAC;CAChD,CAAC;AAEF,oBAAY,kCAAkC;IAC5C,2EAA2E;IAC3E,yBAAyB,gCAAgC;IACzD,2EAA2E;IAC3E,mBAAmB,yBAAyB;IAC5C,yFAAyF;IACzF,kBAAkB,yBAAyB;IAC3C,8DAA8D;IAC9D,mBAAmB,yBAAyB;IAC5C,iEAAiE;IACjE,6BAA6B,oCAAoC;IACjE,qDAAqD;IACrD,+BAA+B,sCAAsC;IACrE,0FAA0F;IAC1F,kBAAkB,wBAAwB;IAC1C,iFAAiF;IACjF,eAAe,qBAAqB;IACpC,6DAA6D;IAC7D,cAAc,oBAAoB;IAClC,qCAAqC;IACrC,kBAAkB,yBAAyB;CAC5C"}