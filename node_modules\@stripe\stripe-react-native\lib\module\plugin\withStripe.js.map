{"version": 3, "names": ["_configPlugins", "require", "_AndroidConfig$Manife", "AndroidConfig", "Manifest", "addMetaDataItemToMainApplication", "getMainApplicationOrThrow", "removeMetaDataItemFromMainApplication", "pkg", "withStripe", "config", "props", "withStripeIos", "withNoopSwiftFile", "withStripeAndroid", "expoConfig", "_ref", "merchantIdentifier", "withEntitlementsPlist", "modResults", "setApplePayEntitlement", "merchantIdentifiers", "entitlements", "_entitlements$key", "key", "merchants", "Array", "isArray", "id", "includes", "push", "length", "exports", "IOSConfig", "XcodeProjectFile", "withBuildSourceFile", "filePath", "contents", "join", "_ref2", "_ref2$enableGooglePay", "enableGooglePay", "withAndroidManifest", "setGooglePayMetaData", "enabled", "GOOGLE_PAY_META_NAME", "mainApplication", "_default", "default", "createRunOncePlugin", "name", "version"], "sourceRoot": "../../../src", "sources": ["plugin/withStripe.ts"], "mappings": "yNAAA,IAAAA,cAAA,CAAAC,OAAA,yBASA,IAAAC,qBAAA,CAIIC,4BAAa,CAACC,QAAQ,CAHxBC,gCAAgC,CAAAH,qBAAA,CAAhCG,gCAAgC,CAChCC,yBAAyB,CAAAJ,qBAAA,CAAzBI,yBAAyB,CACzBC,qCAAqC,CAAAL,qBAAA,CAArCK,qCAAqC,CAGvC,GAAM,CAAAC,GAAG,CAAGP,OAAO,CAAC,0CAA0C,CAAC,CAW/D,GAAM,CAAAQ,UAA2C,CAAG,QAA9C,CAAAA,UAA2CA,CAAIC,MAAM,CAAEC,KAAK,CAAK,CACrED,MAAM,CAAGE,aAAa,CAACF,MAAM,CAAEC,KAAK,CAAC,CACrCD,MAAM,CAAGG,iBAAiB,CAACH,MAAM,CAAC,CAClCA,MAAM,CAAGI,iBAAiB,CAACJ,MAAM,CAAEC,KAAK,CAAC,CACzC,MAAO,CAAAD,MAAM,CACf,CAAC,CAED,GAAM,CAAAE,aAA8C,CAAG,QAAjD,CAAAA,aAA8CA,CAClDG,UAAU,CAAAC,IAAA,CAEP,IADD,CAAAC,kBAAkB,CAAAD,IAAA,CAAlBC,kBAAkB,CAEpB,MAAO,GAAAC,oCAAqB,EAACH,UAAU,CAAE,SAACL,MAAM,CAAK,CACnDA,MAAM,CAACS,UAAU,CAAGC,sBAAsB,CACxCH,kBAAkB,CAClBP,MAAM,CAACS,UACT,CAAC,CACD,MAAO,CAAAT,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAUM,QAAS,CAAAU,sBAAsBA,CACpCC,mBAAsC,CACtCC,YAAiC,CACZ,KAAAC,iBAAA,CACrB,GAAM,CAAAC,GAAG,CAAG,qCAAqC,CAEjD,GAAM,CAAAC,SAAmB,EAAAF,iBAAA,CAAGD,YAAY,CAACE,GAAG,CAAC,QAAAD,iBAAA,CAAI,EAAE,CAEnD,GAAI,CAACG,KAAK,CAACC,OAAO,CAACN,mBAAmB,CAAC,CAAE,CACvCA,mBAAmB,CAAG,CAACA,mBAAmB,CAAC,CAC7C,CAEA,IAAK,GAAM,CAAAO,EAAE,GAAI,CAAAP,mBAAmB,CAAE,CACpC,GAAIO,EAAE,EAAI,CAACH,SAAS,CAACI,QAAQ,CAACD,EAAE,CAAC,CAAE,CACjCH,SAAS,CAACK,IAAI,CAACF,EAAE,CAAC,CACpB,CACF,CAEA,GAAIH,SAAS,CAACM,MAAM,CAAE,CACpBT,YAAY,CAACE,GAAG,CAAC,CAAGC,SAAS,CAC/B,CACA,MAAO,CAAAH,YAAY,CACrB,CAKO,GAAM,CAAAT,iBAA+B,CAAAmB,OAAA,CAAAnB,iBAAA,CAAG,QAAlC,CAAAA,iBAA+BA,CAAIH,MAAM,CAAK,CACzD,MAAO,CAAAuB,wBAAS,CAACC,gBAAgB,CAACC,mBAAmB,CAACzB,MAAM,CAAE,CAC5D0B,QAAQ,CAAE,iBAAiB,CAC3BC,QAAQ,CAAE,CACR,IAAI,CACJ,eAAe,CACf,8FAA8F,CAC9F,IAAI,CACJ,EAAE,CACH,CAACC,IAAI,CAAC,IAAI,CACb,CAAC,CAAC,CACJ,CAAC,CAED,GAAM,CAAAxB,iBAAkD,CAAG,QAArD,CAAAA,iBAAkDA,CACtDC,UAAU,CAAAwB,KAAA,CAEP,KAAAC,qBAAA,CAAAD,KAAA,CADDE,eAAe,CAAfA,eAAe,CAAAD,qBAAA,UAAG,KAAK,CAAAA,qBAAA,CAEzB,MAAO,GAAAE,kCAAmB,EAAC3B,UAAU,CAAE,SAACL,MAAM,CAAK,CACjDA,MAAM,CAACS,UAAU,CAAGwB,oBAAoB,CACtCF,eAAe,CACf/B,MAAM,CAACS,UACT,CAAC,CAED,MAAO,CAAAT,MAAM,CACf,CAAC,CAAC,CACJ,CAAC,CAYM,QAAS,CAAAiC,oBAAoBA,CAClCC,OAAgB,CAChBzB,UAAkD,CACV,CACxC,GAAM,CAAA0B,oBAAoB,CAAG,2CAA2C,CACxE,GAAM,CAAAC,eAAe,CAAGxC,yBAAyB,CAACa,UAAU,CAAC,CAC7D,GAAIyB,OAAO,CAAE,CACXvC,gCAAgC,CAC9ByC,eAAe,CACfD,oBAAoB,CACpB,MACF,CAAC,CACH,CAAC,IAAM,CACLtC,qCAAqC,CACnCuC,eAAe,CACfD,oBACF,CAAC,CACH,CAEA,MAAO,CAAA1B,UAAU,CACnB,CAAC,IAAA4B,QAAA,CAAAf,OAAA,CAAAgB,OAAA,CAEc,GAAAC,kCAAmB,EAACxC,UAAU,CAAED,GAAG,CAAC0C,IAAI,CAAE1C,GAAG,CAAC2C,OAAO,CAAC", "ignoreList": []}