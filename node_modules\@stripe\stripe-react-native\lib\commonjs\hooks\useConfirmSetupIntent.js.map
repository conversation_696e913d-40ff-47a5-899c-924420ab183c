{"version": 3, "names": ["_react", "require", "_useStripe2", "useConfirmSetupIntent", "_useState", "useState", "_useState2", "_slicedToArray2", "default", "loading", "setLoading", "_useStripe", "useStripe", "confirmSetupIntent", "_confirmSetupIntent", "useCallback", "_ref", "_asyncToGenerator2", "paymentIntentClientSecret", "data", "options", "arguments", "length", "undefined", "result", "_x", "_x2", "apply"], "sourceRoot": "../../../src", "sources": ["hooks/useConfirmSetupIntent.tsx"], "mappings": "8XAAA,IAAAA,MAAA,CAAAC,OAAA,UAEA,IAAAC,WAAA,CAAAD,OAAA,gBAKO,QAAS,CAAAE,qBAAqBA,CAAA,CAAG,CACtC,IAAAC,SAAA,CAA8B,GAAAC,eAAQ,EAAC,KAAK,CAAC,CAAAC,UAAA,IAAAC,eAAA,CAAAC,OAAA,EAAAJ,SAAA,IAAtCK,OAAO,CAAAH,UAAA,IAAEI,UAAU,CAAAJ,UAAA,IAC1B,IAAAK,UAAA,CAAmD,GAAAC,qBAAS,EAAC,CAAC,CAAlCC,kBAAkB,CAAAF,UAAA,CAAtCE,kBAAkB,CAE1B,GAAM,CAAAC,mBAAmB,CAAG,GAAAC,kBAAW,iBAAAC,IAAA,IAAAC,kBAAA,CAAAT,OAAA,EACrC,UACEU,yBAAiC,CACjCC,IAA+B,CAE5B,IADH,CAAAC,OAAmC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAExCX,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAc,MAAM,MAAS,CAAAX,kBAAkB,CACrCK,yBAAyB,CACzBC,IAAI,CACJC,OACF,CAAC,CAEDV,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAc,MAAM,CACf,CAAC,kBAAAC,EAAA,CAAAC,GAAA,SAAAV,IAAA,CAAAW,KAAA,MAAAN,SAAA,QACD,CAACR,kBAAkB,CACrB,CAAC,CAED,MAAO,CACLA,kBAAkB,CAAEC,mBAAmB,CACvCL,OAAO,CAAPA,OACF,CAAC,CACH", "ignoreList": []}