/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.Dynamic;
import com.facebook.react.bridge.ReadableArray;

public interface CardFieldManagerInterface<T extends View>  {
  void setAutofocus(T view, boolean value);
  void setCardStyle(T view, Dynamic value);
  void setCountryCode(T view, @Nullable String value);
  void setDangerouslyGetFullCardDetails(T view, boolean value);
  void setDisabled(T view, boolean value);
  void setOnBehalfOf(T view, @Nullable String value);
  void setPlaceholders(T view, Dynamic value);
  void setPostalCodeEnabled(T view, boolean value);
  void setPreferredNetworks(T view, @Nullable ReadableArray value);
  void blur(T view);
  void focus(T view);
  void clear(T view);
}
