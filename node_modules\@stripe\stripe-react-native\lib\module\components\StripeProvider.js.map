{"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_NativeStripeSdkModule", "_interopRequireDefault", "_helpers", "_package", "_reactNative", "_jsxRuntime", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "EXPO_PARTNER_ID", "repository", "pjson", "appInfo", "name", "shouldAttributeExpo", "url", "version", "partnerId", "undefined", "initStripe", "exports", "_ref", "_asyncToGenerator2", "params", "Platform", "OS", "stripeHeadlessTask", "Promise", "AppRegistry", "registerHeadlessTask", "extendedParams", "assign", "NativeStripeSdk", "initialise", "_x", "apply", "arguments", "StripeProvider", "_ref2", "children", "publishableKey", "merchantIdentifier", "threeDSecureParams", "stripeAccountId", "urlScheme", "setReturnUrlSchemeOnAndroid", "useEffect", "isAndroid", "jsx", "Fragment"], "sourceRoot": "../../../src", "sources": ["components/StripeProvider.tsx"], "mappings": "8SAAA,IAAAA,MAAA,CAAAC,uBAAA,CAAAC,OAAA,WAEA,IAAAC,sBAAA,CAAAC,sBAAA,CAAAF,OAAA,oCACA,IAAAG,QAAA,CAAAH,OAAA,eAEA,IAAAI,QAAA,CAAAF,sBAAA,CAAAF,OAAA,2BACA,IAAAK,YAAA,CAAAL,OAAA,iBAAqD,IAAAM,WAAA,CAAAN,OAAA,+BAAAO,yBAAAC,CAAA,wBAAAC,OAAA,iBAAAC,CAAA,KAAAD,OAAA,GAAAE,CAAA,KAAAF,OAAA,UAAAF,wBAAA,UAAAA,yBAAAC,CAAA,SAAAA,CAAA,CAAAG,CAAA,CAAAD,CAAA,IAAAF,CAAA,YAAAT,wBAAAS,CAAA,CAAAE,CAAA,MAAAA,CAAA,EAAAF,CAAA,EAAAA,CAAA,CAAAI,UAAA,QAAAJ,CAAA,WAAAA,CAAA,mBAAAA,CAAA,qBAAAA,CAAA,QAAAK,OAAA,CAAAL,CAAA,MAAAG,CAAA,CAAAJ,wBAAA,CAAAG,CAAA,KAAAC,CAAA,EAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,SAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,MAAAQ,CAAA,EAAAC,SAAA,OAAAC,CAAA,CAAAC,MAAA,CAAAC,cAAA,EAAAD,MAAA,CAAAE,wBAAA,SAAAC,CAAA,IAAAd,CAAA,gBAAAc,CAAA,KAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,CAAAc,CAAA,OAAAG,CAAA,CAAAP,CAAA,CAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,CAAAc,CAAA,OAAAG,CAAA,GAAAA,CAAA,CAAAV,GAAA,EAAAU,CAAA,CAAAC,GAAA,EAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,CAAAM,CAAA,CAAAG,CAAA,EAAAT,CAAA,CAAAM,CAAA,EAAAd,CAAA,CAAAc,CAAA,UAAAN,CAAA,CAAAH,OAAA,CAAAL,CAAA,CAAAG,CAAA,EAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,CAAAQ,CAAA,EAAAA,CAAA,EAErD,GAAM,CAAAW,eAAe,CAAG,2BAA2B,CASnD,GAAM,CAAAC,UAAe,CAAGC,gBAAK,CAACD,UAAU,CAExC,GAAM,CAAAE,OAAgB,CAAG,CACvBC,IAAI,CAAE,GAAAC,4BAAmB,EAAC,CAAC,CAAG,GAAGH,gBAAK,CAACE,IAAI,OAAO,CAAGF,gBAAK,CAACE,IAAI,CAI/DE,GAAG,CAAEL,UAAU,CAACK,GAAG,EAAIL,UAAU,CACjCM,OAAO,CAAEL,gBAAK,CAACK,OAAO,CACtBC,SAAS,CAAE,GAAAH,4BAAmB,EAAC,CAAC,CAAGL,eAAe,CAAGS,SACvD,CAAC,CAEM,GAAM,CAAAC,UAAU,CAAAC,OAAA,CAAAD,UAAA,gBAAAE,IAAA,IAAAC,kBAAA,CAAA3B,OAAA,EAAG,UAAO4B,MAAwB,CAAoB,CAM3E,GAAIC,qBAAQ,CAACC,EAAE,GAAK,SAAS,CAAE,IACpB,CAAAC,kBAAkB,CAA3B,QAAS,CAAAA,kBAAkBA,CAAA,CAAG,CAC5B,MAAO,IAAI,CAAAC,OAAO,CAAO,UAAM,CAAC,CAAC,CAAC,CACpC,CAAC,CAEDC,wBAAW,CAACC,oBAAoB,CAC9B,uBAAuB,CACvB,iBAAM,CAAAH,kBAAkB,EAC1B,CAAC,CACH,CAEA,GAAM,CAAAI,cAAgC,CAAA7B,MAAA,CAAA8B,MAAA,IAAQR,MAAM,EAAEX,OAAO,CAAPA,OAAO,EAAE,CAC/DoB,8BAAe,CAACC,UAAU,CAACH,cAAc,CAAC,CAC5C,CAAC,iBAnBY,CAAAX,UAAUA,CAAAe,EAAA,SAAAb,IAAA,CAAAc,KAAA,MAAAC,SAAA,OAmBtB,CAsBM,QAAS,CAAAC,cAAcA,CAAAC,KAAA,CAQpB,IAPR,CAAAC,QAAQ,CAAAD,KAAA,CAARC,QAAQ,CACRC,cAAc,CAAAF,KAAA,CAAdE,cAAc,CACdC,kBAAkB,CAAAH,KAAA,CAAlBG,kBAAkB,CAClBC,kBAAkB,CAAAJ,KAAA,CAAlBI,kBAAkB,CAClBC,eAAe,CAAAL,KAAA,CAAfK,eAAe,CACfC,SAAS,CAAAN,KAAA,CAATM,SAAS,CACTC,2BAA2B,CAAAP,KAAA,CAA3BO,2BAA2B,CAE3B,GAAAC,gBAAS,EAAC,UAAM,CACd,GAAI,CAACN,cAAc,CAAE,CACnB,OACF,CACA,GAAIO,kBAAS,CAAE,CACbf,8BAAe,CAACC,UAAU,CAAC,CACzBO,cAAc,CAAdA,cAAc,CACd5B,OAAO,CAAPA,OAAO,CACP+B,eAAe,CAAfA,eAAe,CACfD,kBAAkB,CAAlBA,kBAAkB,CAClBE,SAAS,CAATA,SAAS,CACTC,2BAA2B,CAA3BA,2BACF,CAAC,CAAC,CACJ,CAAC,IAAM,CACLb,8BAAe,CAACC,UAAU,CAAC,CACzBO,cAAc,CAAdA,cAAc,CACd5B,OAAO,CAAPA,OAAO,CACP+B,eAAe,CAAfA,eAAe,CACfD,kBAAkB,CAAlBA,kBAAkB,CAClBD,kBAAkB,CAAlBA,kBAAkB,CAClBG,SAAS,CAATA,SACF,CAAC,CAAC,CACJ,CACF,CAAC,CAAE,CACDJ,cAAc,CACdC,kBAAkB,CAClBE,eAAe,CACfD,kBAAkB,CAClBE,SAAS,CACTC,2BAA2B,CAC5B,CAAC,CAEF,MAAO,GAAAzD,WAAA,CAAA4D,GAAA,EAAA5D,WAAA,CAAA6D,QAAA,EAAAV,QAAA,CAAGA,QAAQ,CAAG,CAAC,CACxB", "ignoreList": []}