{"version": 3, "names": ["_react", "require", "_useStripe2", "useConfirmPayment", "_useState", "useState", "_useState2", "_slicedToArray2", "default", "loading", "setLoading", "_useStripe", "useStripe", "confirmPayment", "_confirmPayment", "useCallback", "_ref", "_asyncToGenerator2", "paymentIntentClientSecret", "data", "options", "arguments", "length", "undefined", "result", "_x", "_x2", "apply"], "sourceRoot": "../../../src", "sources": ["hooks/useConfirmPayment.tsx"], "mappings": "sXAAA,IAAAA,MAAA,CAAAC,OAAA,UAEA,IAAAC,WAAA,CAAAD,OAAA,gBAKO,QAAS,CAAAE,iBAAiBA,CAAA,CAAG,CAClC,IAAAC,SAAA,CAA8B,GAAAC,eAAQ,EAAC,KAAK,CAAC,CAAAC,UAAA,IAAAC,eAAA,CAAAC,OAAA,EAAAJ,SAAA,IAAtCK,OAAO,CAAAH,UAAA,IAAEI,UAAU,CAAAJ,UAAA,IAC1B,IAAAK,UAAA,CAA2B,GAAAC,qBAAS,EAAC,CAAC,CAA9BC,cAAc,CAAAF,UAAA,CAAdE,cAAc,CAEtB,GAAM,CAAAC,eAAe,CAAG,GAAAC,kBAAW,iBAAAC,IAAA,IAAAC,kBAAA,CAAAT,OAAA,EACjC,UACEU,yBAAiC,CACjCC,IAAkC,CAE/B,IADH,CAAAC,OAAqC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAE1CX,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAc,MAAM,MAAS,CAAAX,cAAc,CACjCK,yBAAyB,CACzBC,IAAI,CACJC,OACF,CAAC,CAEDV,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAc,MAAM,CACf,CAAC,kBAAAC,EAAA,CAAAC,GAAA,SAAAV,IAAA,CAAAW,KAAA,MAAAN,SAAA,QACD,CAACR,cAAc,CACjB,CAAC,CAED,MAAO,CACLA,cAAc,CAAEC,eAAe,CAC/BL,OAAO,CAAPA,OACF,CAAC,CACH", "ignoreList": []}