Object.defineProperty(exports,"__esModule",{value:true});exports.RowStyle=exports.PaymentMethodLayout=exports.LinkDisplay=exports.CollectionMode=exports.CardBrandCategory=exports.CardBrandAcceptanceFilter=exports.CaptureMethod=exports.AddressCollectionMode=void 0;var LinkDisplay=exports.LinkDisplay=function(LinkDisplay){LinkDisplay["AUTOMATIC"]="automatic";LinkDisplay["NEVER"]="never";return LinkDisplay;}({});var RowStyle=exports.RowStyle=function(RowStyle){RowStyle["FlatWithRadio"]="flatWithRadio";RowStyle["FloatingButton"]="floatingButton";RowStyle["FlatWithCheckmark"]="flatWithCheckmark";return RowStyle;}({});var CollectionMode=exports.CollectionMode=function(CollectionMode){CollectionMode["AUTOMATIC"]="automatic";CollectionMode["NEVER"]="never";CollectionMode["ALWAYS"]="always";return CollectionMode;}({});var AddressCollectionMode=exports.AddressCollectionMode=function(AddressCollectionMode){AddressCollectionMode["AUTOMATIC"]="automatic";AddressCollectionMode["NEVER"]="never";AddressCollectionMode["FULL"]="full";return AddressCollectionMode;}({});var CaptureMethod=exports.CaptureMethod=function(CaptureMethod){CaptureMethod["Automatic"]="Automatic";CaptureMethod["Manual"]="Manual";CaptureMethod["AutomaticAsync"]="AutomaticAsync";return CaptureMethod;}({});var PaymentMethodLayout=exports.PaymentMethodLayout=function(PaymentMethodLayout){PaymentMethodLayout["Horizontal"]="Horizontal";PaymentMethodLayout["Vertical"]="Vertical";PaymentMethodLayout["Automatic"]="Automatic";return PaymentMethodLayout;}({});var CardBrandCategory=exports.CardBrandCategory=function(CardBrandCategory){CardBrandCategory["Visa"]="visa";CardBrandCategory["Mastercard"]="mastercard";CardBrandCategory["Amex"]="amex";CardBrandCategory["Discover"]="discover";return CardBrandCategory;}({});var CardBrandAcceptanceFilter=exports.CardBrandAcceptanceFilter=function(CardBrandAcceptanceFilter){CardBrandAcceptanceFilter["All"]="all";CardBrandAcceptanceFilter["Allowed"]="allowed";CardBrandAcceptanceFilter["Disallowed"]="disallowed";return CardBrandAcceptanceFilter;}({});
//# sourceMappingURL=PaymentSheet.js.map