Object.defineProperty(exports,"__esModule",{value:true});exports.FinancialConnectionsSheetError=exports.FinancialConnectionsEventName=exports.FinancialConnectionsEventErrorCode=void 0;var FinancialConnectionsSheetError=exports.FinancialConnectionsSheetError=function(FinancialConnectionsSheetError){FinancialConnectionsSheetError["Failed"]="Failed";FinancialConnectionsSheetError["Canceled"]="Canceled";return FinancialConnectionsSheetError;}({});var FinancialConnectionsEventName=exports.FinancialConnectionsEventName=function(FinancialConnectionsEventName){FinancialConnectionsEventName["Open"]="open";FinancialConnectionsEventName["ManualEntryInitiated"]="manual_entry_initiated";FinancialConnectionsEventName["ConsentAcquired"]="consent_acquired";FinancialConnectionsEventName["SearchInitiated"]="search_initiated";FinancialConnectionsEventName["InstitutionSelected"]="institution_selected";FinancialConnectionsEventName["InstitutionAuthorized"]="institution_authorized";FinancialConnectionsEventName["AccountsSelected"]="accounts_selected";FinancialConnectionsEventName["Success"]="success";FinancialConnectionsEventName["Error"]="error";FinancialConnectionsEventName["Cancel"]="cancel";FinancialConnectionsEventName["FlowLaunchedInBrowser"]="flow_launched_in_browser";return FinancialConnectionsEventName;}({});var FinancialConnectionsEventErrorCode=exports.FinancialConnectionsEventErrorCode=function(FinancialConnectionsEventErrorCode){FinancialConnectionsEventErrorCode["AccountNumbersUnavailable"]="account_numbers_unavailable";FinancialConnectionsEventErrorCode["AccountsUnavailable"]="accounts_unavailable";FinancialConnectionsEventErrorCode["NoDebitableAccount"]="no_debitable_account";FinancialConnectionsEventErrorCode["AuthorizationFailed"]="authorization_failed";FinancialConnectionsEventErrorCode["InstitutionUnavailablePlanned"]="institution_unavailable_planned";FinancialConnectionsEventErrorCode["InstitutionUnavailableUnplanned"]="institution_unavailable_unplanned";FinancialConnectionsEventErrorCode["InstitutionTimeout"]="institution_timeout";FinancialConnectionsEventErrorCode["UnexpectedError"]="unexpected_error";FinancialConnectionsEventErrorCode["SessionExpired"]="session_expired";FinancialConnectionsEventErrorCode["FailedBotDetection"]="failed_bot_detection";return FinancialConnectionsEventErrorCode;}({});
//# sourceMappingURL=FinancialConnections.js.map