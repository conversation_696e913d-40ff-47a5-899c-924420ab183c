var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.useFinancialConnectionsSheet=useFinancialConnectionsSheet;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _react=require("react");var _useStripe2=require("./useStripe");function useFinancialConnectionsSheet(){var _useState=(0,_react.useState)(false),_useState2=(0,_slicedToArray2.default)(_useState,2),loading=_useState2[0],setLoading=_useState2[1];var _useStripe=(0,_useStripe2.useStripe)(),collectBankAccountToken=_useStripe.collectBankAccountToken,collectFinancialConnectionsAccounts=_useStripe.collectFinancialConnectionsAccounts;var _collectBankAccountToken=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(clientSecret,params){setLoading(true);var result=yield collectBankAccountToken(clientSecret,params);setLoading(false);return result;});return function(_x,_x2){return _ref.apply(this,arguments);};}(),[collectBankAccountToken]);var _collectFinancialConnectionsAccounts=(0,_react.useCallback)(function(){var _ref2=(0,_asyncToGenerator2.default)(function*(clientSecret,params){setLoading(true);var result=yield collectFinancialConnectionsAccounts(clientSecret,params);setLoading(false);return result;});return function(_x3,_x4){return _ref2.apply(this,arguments);};}(),[collectFinancialConnectionsAccounts]);return{collectBankAccountToken:_collectBankAccountToken,collectFinancialConnectionsAccounts:_collectFinancialConnectionsAccounts,loading:loading};}
//# sourceMappingURL=useFinancialConnectionsSheet.js.map