/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.Dynamic;
import com.facebook.react.bridge.ReadableMap;

public interface AddToWalletButtonManagerInterface<T extends View>  {
  void setIOSButtonStyle(T view, @Nullable String value);
  void setAndroidAssetSource(T view, @Nullable ReadableMap value);
  void setTestEnv(T view, boolean value);
  void setCardDetails(T view, Dynamic value);
  void setToken(T view, Dynamic value);
  void setEphemeralKey(T view, Dynamic value);
}
