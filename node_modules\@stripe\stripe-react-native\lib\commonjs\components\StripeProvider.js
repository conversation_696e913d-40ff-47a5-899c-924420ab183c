var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.StripeProvider=StripeProvider;exports.initStripe=void 0;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _react=_interopRequireWildcard(require("react"));var _NativeStripeSdkModule=_interopRequireDefault(require("../specs/NativeStripeSdkModule"));var _helpers=require("../helpers");var _package=_interopRequireDefault(require("../../../package.json"));var _reactNative=require("react-native");var _jsxRuntime=require("react/jsx-runtime");function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var EXPO_PARTNER_ID='pp_partner_JBN7LkABco2yUu';var repository=_package.default.repository;var appInfo={name:(0,_helpers.shouldAttributeExpo)()?`${_package.default.name}/expo`:_package.default.name,url:repository.url||repository,version:_package.default.version,partnerId:(0,_helpers.shouldAttributeExpo)()?EXPO_PARTNER_ID:undefined};var initStripe=exports.initStripe=function(){var _ref=(0,_asyncToGenerator2.default)(function*(params){if(_reactNative.Platform.OS==='android'){var stripeHeadlessTask=function stripeHeadlessTask(){return new Promise(function(){});};_reactNative.AppRegistry.registerHeadlessTask('StripeKeepJsAwakeTask',function(){return stripeHeadlessTask;});}var extendedParams=Object.assign({},params,{appInfo:appInfo});_NativeStripeSdkModule.default.initialise(extendedParams);});return function initStripe(_x){return _ref.apply(this,arguments);};}();function StripeProvider(_ref2){var children=_ref2.children,publishableKey=_ref2.publishableKey,merchantIdentifier=_ref2.merchantIdentifier,threeDSecureParams=_ref2.threeDSecureParams,stripeAccountId=_ref2.stripeAccountId,urlScheme=_ref2.urlScheme,setReturnUrlSchemeOnAndroid=_ref2.setReturnUrlSchemeOnAndroid;(0,_react.useEffect)(function(){if(!publishableKey){return;}if(_helpers.isAndroid){_NativeStripeSdkModule.default.initialise({publishableKey:publishableKey,appInfo:appInfo,stripeAccountId:stripeAccountId,threeDSecureParams:threeDSecureParams,urlScheme:urlScheme,setReturnUrlSchemeOnAndroid:setReturnUrlSchemeOnAndroid});}else{_NativeStripeSdkModule.default.initialise({publishableKey:publishableKey,appInfo:appInfo,stripeAccountId:stripeAccountId,threeDSecureParams:threeDSecureParams,merchantIdentifier:merchantIdentifier,urlScheme:urlScheme});}},[publishableKey,merchantIdentifier,stripeAccountId,threeDSecureParams,urlScheme,setReturnUrlSchemeOnAndroid]);return(0,_jsxRuntime.jsx)(_jsxRuntime.Fragment,{children:children});}
//# sourceMappingURL=StripeProvider.js.map