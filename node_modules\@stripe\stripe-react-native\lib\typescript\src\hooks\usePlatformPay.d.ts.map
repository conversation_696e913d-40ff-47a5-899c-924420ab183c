{"version": 3, "file": "usePlatformPay.d.ts", "sourceRoot": "", "sources": ["../../../../src/hooks/usePlatformPay.tsx"], "names": [], "mappings": "AACA,OAAO,KAAK,EACV,WAAW,EACX,wBAAwB,EACxB,wBAAwB,EACzB,MAAM,UAAU,CAAC;AAGlB;;GAEG;AACH,wBAAgB,cAAc;IAwH1B,8IAA8I;;IAE9I;;;OAGG;sCA9Ga;QAAE,SAAS,CAAC,EAAE,WAAW,CAAC,0BAA0B,CAAA;KAAE;IAgHtE;;;;;OAKG;kDAzGkB,MAAM,UAAU,WAAW,CAAC,aAAa;IA2G9D;;;;;OAKG;8CApGkB,MAAM,UAAU,WAAW,CAAC,aAAa;IAsG9D;;;;OAIG;6CA9FY,WAAW,CAAC,mBAAmB;IAgG9C;;;;OAIG;qCAxFY,WAAW,CAAC,mBAAmB;IA0F9C;;;OAGG;;IAEH;;;;;;;OAOG;qCAjFY;QACb,QAAQ,EAAE;YACR,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YAC9C,eAAe,EAAE,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;SAC/C,CAAC;KACH;;;IA6ED;;;;;OAKG;iCArEO,wBAAwB,KAC/B,OAAO,CAAC,wBAAwB,CAAC;IAsEpC;;;;OAIG;gCA/D+C,OAAO,CAAC,IAAI,CAAC;EAkElE"}