var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.useConfirmPayment=useConfirmPayment;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _react=require("react");var _useStripe2=require("./useStripe");function useConfirmPayment(){var _useState=(0,_react.useState)(false),_useState2=(0,_slicedToArray2.default)(_useState,2),loading=_useState2[0],setLoading=_useState2[1];var _useStripe=(0,_useStripe2.useStripe)(),confirmPayment=_useStripe.confirmPayment;var _confirmPayment=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,data){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};setLoading(true);var result=yield confirmPayment(paymentIntentClientSecret,data,options);setLoading(false);return result;});return function(_x,_x2){return _ref.apply(this,arguments);};}(),[confirmPayment]);return{confirmPayment:_confirmPayment,loading:loading};}
//# sourceMappingURL=useConfirmPayment.js.map