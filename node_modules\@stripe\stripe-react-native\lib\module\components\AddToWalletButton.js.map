{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_NativeAddToWalletButton", "_jsxRuntime", "_jsxFileName", "_excluded", "AddToWalletButton", "_ref", "onComplete", "props", "_objectWithoutProperties2", "default", "jsx", "Object", "assign", "onCompleteAction", "value", "nativeEvent"], "sourceRoot": "../../../src", "sources": ["components/AddToWalletButton.tsx"], "mappings": "wSAAA,IAAAA,MAAA,CAAAC,sBAAA,CAAAC,OAAA,WAcA,IAAAC,wBAAA,CAAAF,sBAAA,CAAAC,OAAA,sCAAuE,IAAAE,WAAA,CAAAF,OAAA,0BAAAG,YAAA,qFAAAC,SAAA,gBAgEhE,QAAS,CAAAC,iBAAiBA,CAAAC,IAAA,CAAkC,IAA/B,CAAAC,UAAU,CAAAD,IAAA,CAAVC,UAAU,CAAKC,KAAK,IAAAC,yBAAA,CAAAC,OAAA,EAAAJ,IAAA,CAAAF,SAAA,EACtD,MACE,GAAAF,WAAA,CAAAS,GAAA,EAACV,wBAAA,CAAAS,OAAuB,CAAAE,MAAA,CAAAC,MAAA,IAClBL,KAAK,EACTM,gBAAgB,CAAE,QAAlB,CAAAA,gBAAgBA,CACdC,KAEE,QACC,CAAAR,UAAU,CAACQ,KAAK,CAACC,WAAW,CAAC,EAAC,EACpC,CAAC,CAEN", "ignoreList": []}