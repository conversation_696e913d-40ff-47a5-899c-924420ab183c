var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=void 0;var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var _require=require('react-native/Libraries/NativeComponent/ViewConfigIgnore'),ConditionallyIgnoredEventHandlers=_require.ConditionallyIgnoredEventHandlers;var nativeComponentName='ApplePayButton';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'ApplePayButton',directEventTypes:{topShippingMethodSelectedAction:{registrationName:'onShippingMethodSelectedAction'},topShippingContactSelectedAction:{registrationName:'onShippingContactSelectedAction'},topCouponCodeEnteredAction:{registrationName:'onCouponCodeEnteredAction'},topOrderTrackingAction:{registrationName:'onOrderTrackingAction'}},validAttributes:Object.assign({disabled:true,type:true,buttonStyle:true,borderRadius:true},ConditionallyIgnoredEventHandlers({onShippingMethodSelectedAction:true,onShippingContactSelectedAction:true,onCouponCodeEnteredAction:true,onOrderTrackingAction:true}))};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});
//# sourceMappingURL=NativeApplePayButton.js.map