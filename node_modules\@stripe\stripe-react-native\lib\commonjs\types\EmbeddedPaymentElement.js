var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.useEmbeddedPaymentElement=useEmbeddedPaymentElement;var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _classCallCheck2=_interopRequireDefault(require("@babel/runtime/helpers/classCallCheck"));var _createClass2=_interopRequireDefault(require("@babel/runtime/helpers/createClass"));var _reactNative=require("react-native");var _NativeStripeSdkModule=_interopRequireDefault(require("../specs/NativeStripeSdkModule"));var _react=_interopRequireWildcard(require("react"));var _events=require("../events");var _NativeEmbeddedPaymentElement=_interopRequireWildcard(require("../specs/NativeEmbeddedPaymentElement"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/types/EmbeddedPaymentElement.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var EmbeddedPaymentElement=function(){function EmbeddedPaymentElement(){(0,_classCallCheck2.default)(this,EmbeddedPaymentElement);}return(0,_createClass2.default)(EmbeddedPaymentElement,[{key:"update",value:(function(){var _update=(0,_asyncToGenerator2.default)(function*(intentConfig){var result=yield _NativeStripeSdkModule.default.updateEmbeddedPaymentElement(intentConfig);return result;});function update(_x){return _update.apply(this,arguments);}return update;}())},{key:"confirm",value:(function(){var _confirm=(0,_asyncToGenerator2.default)(function*(){var result=yield _NativeStripeSdkModule.default.confirmEmbeddedPaymentElement(-1);return result;});function confirm(){return _confirm.apply(this,arguments);}return confirm;}())},{key:"clearPaymentOption",value:function clearPaymentOption(){_NativeStripeSdkModule.default.clearEmbeddedPaymentOption(-1);}}]);}();var confirmHandlerCallback=null;var formSheetActionConfirmCallback=null;function createEmbeddedPaymentElement(_x2,_x3){return _createEmbeddedPaymentElement.apply(this,arguments);}function _createEmbeddedPaymentElement(){_createEmbeddedPaymentElement=(0,_asyncToGenerator2.default)(function*(intentConfig,configuration){setupConfirmHandlers(intentConfig,configuration);yield _NativeStripeSdkModule.default.createEmbeddedPaymentElement(intentConfig,configuration);return new EmbeddedPaymentElement();});return _createEmbeddedPaymentElement.apply(this,arguments);}function setupConfirmHandlers(intentConfig,configuration){var _configuration$formSh;var confirmHandler=intentConfig.confirmHandler;if(confirmHandler){var _confirmHandlerCallba;(_confirmHandlerCallba=confirmHandlerCallback)==null||_confirmHandlerCallba.remove();confirmHandlerCallback=(0,_events.addListener)('onConfirmHandlerCallback',function(_ref){var paymentMethod=_ref.paymentMethod,shouldSavePaymentMethod=_ref.shouldSavePaymentMethod;confirmHandler(paymentMethod,shouldSavePaymentMethod,_NativeStripeSdkModule.default.intentCreationCallback);});}if(((_configuration$formSh=configuration.formSheetAction)==null?void 0:_configuration$formSh.type)==='confirm'){var confirmFormSheetHandler=configuration.formSheetAction.onFormSheetConfirmComplete;if(confirmFormSheetHandler){var _formSheetActionConfi;(_formSheetActionConfi=formSheetActionConfirmCallback)==null||_formSheetActionConfi.remove();formSheetActionConfirmCallback=(0,_events.addListener)('embeddedPaymentElementFormSheetConfirmComplete',function(result){confirmFormSheetHandler(result);});}}}function useEmbeddedPaymentElement(intentConfig,configuration){var _this=this;var isAndroid=_reactNative.Platform.OS==='android';var elementRef=(0,_react.useRef)(null);var _useState=(0,_react.useState)(null),_useState2=(0,_slicedToArray2.default)(_useState,2),element=_useState2[0],setElement=_useState2[1];var _useState3=(0,_react.useState)(null),_useState4=(0,_slicedToArray2.default)(_useState3,2),paymentOption=_useState4[0],setPaymentOption=_useState4[1];var _useState5=(0,_react.useState)(),_useState6=(0,_slicedToArray2.default)(_useState5,2),height=_useState6[0],setHeight=_useState6[1];var viewRef=(0,_react.useRef)(null);var _useState7=(0,_react.useState)(null),_useState8=(0,_slicedToArray2.default)(_useState7,2),loadingError=_useState8[0],setLoadingError=_useState8[1];function getElementOrThrow(ref){if(!ref.current){throw new Error('EmbeddedPaymentElement is not ready yet – wait until it finishes loading before calling this API.');}return ref.current;}(0,_react.useEffect)(function(){var active=true;(0,_asyncToGenerator2.default)(function*(){var el=yield createEmbeddedPaymentElement(intentConfig,configuration);if(!active)return;elementRef.current=el;setElement(el);})();var getCurrentRef=function getCurrentRef(){return viewRef.current;};return function(){var _elementRef$current;active=false;(_elementRef$current=elementRef.current)==null||_elementRef$current.clearPaymentOption();elementRef.current=null;var currentRef=getCurrentRef();if(isAndroid&&currentRef){_NativeEmbeddedPaymentElement.Commands.clearPaymentOption(currentRef);}setElement(null);};},[intentConfig,configuration,viewRef,isAndroid]);(0,_react.useEffect)(function(){var sub=(0,_events.addListener)('embeddedPaymentElementDidUpdatePaymentOption',function(_ref3){var opt=_ref3.paymentOption;return setPaymentOption(opt!=null?opt:null);});return function(){return sub.remove();};});(0,_react.useEffect)(function(){var sub=(0,_events.addListener)('embeddedPaymentElementDidUpdateHeight',function(_ref4){var h=_ref4.height;if(h>0||isAndroid&&h===0){_reactNative.LayoutAnimation.configureNext(_reactNative.LayoutAnimation.Presets.easeInEaseOut);setHeight(h);}});return function(){return sub.remove();};},[isAndroid]);(0,_react.useEffect)(function(){var sub=(0,_events.addListener)('embeddedPaymentElementLoadingFailed',function(nativeError){setLoadingError(new Error(nativeError.message));});return function(){return sub.remove();};},[]);var embeddedPaymentElementView=(0,_react.useMemo)(function(){if(isAndroid&&configuration&&intentConfig){return(0,_jsxRuntime.jsx)(_NativeEmbeddedPaymentElement.default,{ref:viewRef,style:[{width:'100%',height:height}],configuration:configuration,intentConfiguration:intentConfig});}if(!element)return null;return(0,_jsxRuntime.jsx)(_NativeEmbeddedPaymentElement.default,{ref:viewRef,style:{width:'100%',height:height},configuration:configuration,intentConfiguration:intentConfig});},[configuration,element,height,intentConfig,isAndroid]);var confirm=(0,_react.useCallback)(function(){var currentRef=viewRef.current;if(isAndroid){if(currentRef){var promise=new Promise(function(resolve){var sub=(0,_events.addListener)('embeddedPaymentElementFormSheetConfirmComplete',function(result){sub.remove();resolve(result);});});_NativeEmbeddedPaymentElement.Commands.confirm(currentRef);return promise;}else{return Promise.reject(new Error('Unable to find Android embedded payment element view!'));}}return getElementOrThrow(elementRef).confirm();},[isAndroid]);var update=(0,_react.useCallback)(function(cfg){return getElementOrThrow(elementRef).update(cfg);},[]);var clearPaymentOption=(0,_react.useCallback)(function(){if(isAndroid){var tag=(0,_reactNative.findNodeHandle)(viewRef.current);if(tag==null){return Promise.reject(new Error('Unable to find Android view handle'));}return _NativeStripeSdkModule.default.clearEmbeddedPaymentOption(tag);}getElementOrThrow(elementRef).clearPaymentOption();return Promise.resolve();},[isAndroid]);return{embeddedPaymentElementView:embeddedPaymentElementView,paymentOption:paymentOption,confirm:confirm,update:update,clearPaymentOption:clearPaymentOption,loadingError:loadingError};}
//# sourceMappingURL=EmbeddedPaymentElement.js.map