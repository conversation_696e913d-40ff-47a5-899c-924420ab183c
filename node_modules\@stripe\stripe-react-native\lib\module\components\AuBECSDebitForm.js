var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.AuBECSDebitForm=AuBECSDebitForm;var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireDefault(require("react"));var _NativeAuBECSDebitForm=_interopRequireDefault(require("../specs/NativeAuBECSDebitForm"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/AuBECSDebitForm.tsx";var _excluded=["onComplete","companyName","formStyle"];function AuBECSDebitForm(_ref){var onComplete=_ref.onComplete,companyName=_ref.companyName,formStyle=_ref.formStyle,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);return(0,_jsxRuntime.jsx)(_NativeAuBECSDebitForm.default,Object.assign({onCompleteAction:function onCompleteAction(value){return onComplete(value.nativeEvent);},companyName:companyName,formStyle:Object.assign({},formStyle)},props));}
//# sourceMappingURL=AuBECSDebitForm.js.map