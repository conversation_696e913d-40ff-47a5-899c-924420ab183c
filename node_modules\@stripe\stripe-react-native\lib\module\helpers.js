var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.createError=createError;exports.unsupportedMethodMessage=exports.unregisterInput=exports.shouldAttributeExpo=exports.registerInput=exports.isiOS=exports.isAndroid=exports.focusInput=exports.currentlyFocusedInput=void 0;var _reactNative=require("react-native");var _TextInputState=_interopRequireDefault(require("react-native/Libraries/Components/TextInput/TextInputState"));var shouldAttributeExpo=exports.shouldAttributeExpo=function shouldAttributeExpo(){try{return!!_reactNative.NativeModules.NativeUnimoduleProxy;}catch(_unused){return false;}};var isiOS=exports.isiOS=_reactNative.Platform.OS==='ios';var isAndroid=exports.isAndroid=_reactNative.Platform.OS==='android';function createError(error){return{code:error.code,message:error.message,localizedMessage:error.localizedMessage,declineCode:error.declineCode,stripeErrorCode:error.stripeErrorCode,type:error.type};}var unsupportedMethodMessage=exports.unsupportedMethodMessage=function unsupportedMethodMessage(field){return`${field} method is not supported. Consider to upgrade react-native version to 0.63.x or higher`;};var focusInput=exports.focusInput=function focusInput(ref){if('focusInput'in _TextInputState.default){_TextInputState.default.focusInput(ref);}else{if(__DEV__){console.log(unsupportedMethodMessage('focusInput'));}}};var registerInput=exports.registerInput=function registerInput(ref){if('registerInput'in _TextInputState.default){_TextInputState.default.registerInput(ref);}else{if(__DEV__){console.log(unsupportedMethodMessage('registerInput'));}}};var unregisterInput=exports.unregisterInput=function unregisterInput(ref){if('unregisterInput'in _TextInputState.default){_TextInputState.default.unregisterInput(ref);}else{if(__DEV__){console.log(unsupportedMethodMessage('unregisterInput'));}}};var currentlyFocusedInput=exports.currentlyFocusedInput=function currentlyFocusedInput(){if('currentlyFocusedInput'in _TextInputState.default){return _TextInputState.default.currentlyFocusedInput();}else{if(__DEV__){console.log(unsupportedMethodMessage('currentlyFocusedInput'));}}};
//# sourceMappingURL=helpers.js.map