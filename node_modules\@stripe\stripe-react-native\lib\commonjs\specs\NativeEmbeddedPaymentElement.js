var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=exports.Commands=void 0;var _codegenNativeCommands=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeCommands"));var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var _require=require("react-native/Libraries/ReactNative/RendererProxy"),dispatchCommand=_require.dispatchCommand;var nativeComponentName='EmbeddedPaymentElementView';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'EmbeddedPaymentElementView',validAttributes:{configuration:true,intentConfiguration:true}};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});var Commands=exports.Commands={confirm:function confirm(ref){dispatchCommand(ref,"confirm",[]);},clearPaymentOption:function clearPaymentOption(ref){dispatchCommand(ref,"clearPaymentOption",[]);}};
//# sourceMappingURL=NativeEmbeddedPaymentElement.js.map