{"name": "@stripe/stripe-react-native", "version": "0.48.0", "author": "Stripe", "description": "Stripe SDK for React Native", "main": "lib/commonjs/index", "module": "lib/module/index", "types": "lib/typescript/src/index.d.ts", "react-native": "src/index", "source": "src/index", "scripts": {"test": "jest", "typescript": "tsc --noEmit", "lint": "eslint \"**/*.{js,ts,tsx}\" --ignore-pattern \"docs/api-reference/*\" --ignore-path .gitignore", "prepare": "bob build && husky", "release": "./scripts/publish", "example": "yarn --cwd example", "pods": "cd example && npx pod-install --quiet", "update-pods": "cd example/ios && pod update Stripe StripeApplePay StripeFinancialConnections StripePayments StripePaymentSheet StripePaymentsUI StripeCore StripeUICore", "bootstrap": "yarn example && yarn && yarn pods", "bootstrap-no-pods": "yarn example && yarn", "docs": "yarn typedoc ./src/index.tsx --out ./docs/api-reference --tsconfig ./tsconfig.json --readme none --sort source-order", "run-example-ios": "cd example && yarn ios --simulator \"iPhone 16\"", "run-example-ios:release": "cd example && yarn build:ios && yarn ios --mode Release --simulator \"iPhone 16 (18.2)\" --no-packager", "run-example-android": "cd example && yarn android", "run-example-android:release": "cd example && yarn build:android && yarn android --mode=release --no-packager", "test:e2e:ios": "bash ./scripts/run-maestro-tests ios", "test:e2e:android": "bash ./scripts/run-maestro-tests android", "test:unit:ios": "xcodebuild test -workspace example/ios/example.xcworkspace -destination 'platform=iOS Simulator,name=iPhone 16' -scheme stripe-react-native-Unit-Tests", "test:unit:android": "cd example/android && ./gradlew connectedAndroidTest", "test-ios": "maestro test -e APP_ID=com.stripe.react.native", "test-android": "maestro test -e APP_ID=com.stripe.react.native", "format:android:check": "cd android && ./gradlew spotlessCheck", "format:android:write": "cd android && ./gradlew spotlessApply"}, "keywords": ["react-native", "ios", "android"], "repository": "https://github.com/stripe/stripe-react-native", "license": "MIT", "bugs": {"url": "https://github.com/stripe/stripe-react-native/issues"}, "homepage": "https://github.com/stripe/stripe-react-native/#readme", "dependencies": {}, "devDependencies": {"@expo/config-plugins": "^9.0.16", "@react-native/babel-preset": "^0.78.0", "@react-native/eslint-config": "^0.78.0", "@types/jest": "^29.5.14", "@types/react": "^19.0.10", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "husky": "^9.1.7", "jest": "^29.7.0", "prettier": "^3.5.2", "react": "^19.0.0", "react-native": "^0.78.0", "react-native-builder-bob": "^0.37.0", "ts-node": "^10.9.2", "typedoc": "^0.27.9", "typescript": "^5.7.3"}, "peerDependencies": {"expo": ">=46.0.9", "react": "*", "react-native": "*"}, "peerDependenciesMeta": {"expo": {"optional": true}}, "jest": {"preset": "react-native", "modulePathIgnorePatterns": ["<rootDir>/example/node_modules", "<rootDir>/lib/", "<rootDir>/e2e"]}, "eslintConfig": {"extends": ["@react-native", "plugin:prettier/recommended"], "rules": {"no-shadow": "off", "@typescript-eslint/no-shadow": ["error"], "no-restricted-syntax": ["error", {"selector": "TSEnumDeclaration[const=true]", "message": "`const enum` declarations are not allowed"}]}}, "eslintIgnore": ["node_modules/", "lib/", "dist/", "server-dist/"], "prettier": {"quoteProps": "consistent", "singleQuote": true, "tabWidth": 2, "trailingComma": "es5", "useTabs": false}, "react-native-builder-bob": {"source": "src", "output": "lib", "targets": [["commonjs", {"configFile": true}], ["module", {"configFile": true}], ["typescript", {"configFile": true}]]}, "resolutions": {"simple-plist": "1.3.1"}, "codegenConfig": {"android": {"javaPackageName": "com.reactnativestripesdk"}, "ios": {"componentProvider": {"ApplePayButton": "ApplePayButtonComponentView", "AddToWalletButton": "AddToWalletButtonComponentView", "AddressSheetView": "AddressSheetViewComponentView", "AuBECSDebitForm": "AuBECSDebitFormComponentView", "CardField": "CardFieldComponentView", "CardForm": "CardFormComponentView", "EmbeddedPaymentElementView": "EmbeddedPaymentElementViewComponentView", "StripeContainer": "StripeContainerComponentView"}}, "name": "rnstripe", "type": "all", "jsSrcsDir": "./src/specs"}}