var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.useStripe=useStripe;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _react=require("react");var _functions=require("../functions");function useStripe(){var _createPaymentMethod=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(data){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return(0,_functions.createPaymentMethod)(data,options);});return function(_x){return _ref.apply(this,arguments);};}(),[]);var _createToken=(0,_react.useCallback)(function(){var _ref2=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.createToken)(params);});return function(_x2){return _ref2.apply(this,arguments);};}(),[]);var _retrievePaymentIntent=(0,_react.useCallback)(function(){var _ref3=(0,_asyncToGenerator2.default)(function*(clientSecret){return(0,_functions.retrievePaymentIntent)(clientSecret);});return function(_x3){return _ref3.apply(this,arguments);};}(),[]);var _retrieveSetupIntent=(0,_react.useCallback)(function(){var _ref4=(0,_asyncToGenerator2.default)(function*(clientSecret){return(0,_functions.retrieveSetupIntent)(clientSecret);});return function(_x4){return _ref4.apply(this,arguments);};}(),[]);var _confirmPayment=(0,_react.useCallback)(function(){var _ref5=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,data){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};return(0,_functions.confirmPayment)(paymentIntentClientSecret,data,options);});return function(_x5,_x6){return _ref5.apply(this,arguments);};}(),[]);var _handleNextAction=(0,_react.useCallback)(function(){var _ref6=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,returnURL){return(0,_functions.handleNextAction)(paymentIntentClientSecret,returnURL);});return function(_x7,_x8){return _ref6.apply(this,arguments);};}(),[]);var _handleNextActionForSetup=(0,_react.useCallback)(function(){var _ref7=(0,_asyncToGenerator2.default)(function*(setupIntentClientSecret,returnURL){return(0,_functions.handleNextActionForSetup)(setupIntentClientSecret,returnURL);});return function(_x9,_x10){return _ref7.apply(this,arguments);};}(),[]);var _confirmSetupIntent=(0,_react.useCallback)(function(){var _ref8=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,data){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};return(0,_functions.confirmSetupIntent)(paymentIntentClientSecret,data,options);});return function(_x11,_x12){return _ref8.apply(this,arguments);};}(),[]);var _createTokenForCVCUpdate=(0,_react.useCallback)(function(){var _ref9=(0,_asyncToGenerator2.default)(function*(cvc){return(0,_functions.createTokenForCVCUpdate)(cvc);});return function(_x13){return _ref9.apply(this,arguments);};}(),[]);var _initPaymentSheet=(0,_react.useCallback)(function(){var _ref10=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.initPaymentSheet)(params);});return function(_x14){return _ref10.apply(this,arguments);};}(),[]);var _presentPaymentSheet=(0,_react.useCallback)(function(){var _ref11=(0,_asyncToGenerator2.default)(function*(options){return(0,_functions.presentPaymentSheet)(options);});return function(_x15){return _ref11.apply(this,arguments);};}(),[]);var _confirmPaymentSheetPayment=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){return(0,_functions.confirmPaymentSheetPayment)();}),[]);var _handleURLCallback=(0,_react.useCallback)(function(){var _ref13=(0,_asyncToGenerator2.default)(function*(url){return(0,_functions.handleURLCallback)(url);});return function(_x16){return _ref13.apply(this,arguments);};}(),[]);var _collectBankAccountForPayment=(0,_react.useCallback)(function(){var _ref14=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.collectBankAccountForPayment)(clientSecret,params);});return function(_x17,_x18){return _ref14.apply(this,arguments);};}(),[]);var _collectBankAccountForSetup=(0,_react.useCallback)(function(){var _ref15=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.collectBankAccountForSetup)(clientSecret,params);});return function(_x19,_x20){return _ref15.apply(this,arguments);};}(),[]);var _verifyMicrodepositsForPayment=(0,_react.useCallback)(function(){var _ref16=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.verifyMicrodepositsForPayment)(clientSecret,params);});return function(_x21,_x22){return _ref16.apply(this,arguments);};}(),[]);var _verifyMicrodepositsForSetup=(0,_react.useCallback)(function(){var _ref17=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.verifyMicrodepositsForSetup)(clientSecret,params);});return function(_x23,_x24){return _ref17.apply(this,arguments);};}(),[]);var _canAddCardToWallet=(0,_react.useCallback)(function(){var _ref18=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.canAddCardToWallet)(params);});return function(_x25){return _ref18.apply(this,arguments);};}(),[]);var _collectBankAccountToken=(0,_react.useCallback)(function(){var _ref19=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.collectBankAccountToken)(clientSecret,params);});return function(_x26,_x27){return _ref19.apply(this,arguments);};}(),[]);var _collectFinancialConnectionsAccounts=(0,_react.useCallback)(function(){var _ref20=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.collectFinancialConnectionsAccounts)(clientSecret,params);});return function(_x28,_x29){return _ref20.apply(this,arguments);};}(),[]);var _resetPaymentSheetCustomer=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){return(0,_functions.resetPaymentSheetCustomer)();}),[]);var _isPlatformPaySupported=(0,_react.useCallback)(function(){var _ref22=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.isPlatformPaySupported)(params);});return function(_x30){return _ref22.apply(this,arguments);};}(),[]);var _confirmPlatformPaySetupIntent=(0,_react.useCallback)(function(){var _ref23=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.confirmPlatformPaySetupIntent)(clientSecret,params);});return function(_x31,_x32){return _ref23.apply(this,arguments);};}(),[]);var _confirmPlatformPayPayment=(0,_react.useCallback)(function(){var _ref24=(0,_asyncToGenerator2.default)(function*(clientSecret,params){return(0,_functions.confirmPlatformPayPayment)(clientSecret,params);});return function(_x33,_x34){return _ref24.apply(this,arguments);};}(),[]);var _dismissPlatformPay=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){return(0,_functions.dismissPlatformPay)();}),[]);var _createPlatformPayPaymentMethod=(0,_react.useCallback)(function(){var _ref26=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.createPlatformPayPaymentMethod)(params);});return function(_x35){return _ref26.apply(this,arguments);};}(),[]);var _createPlatformPayToken=(0,_react.useCallback)(function(){var _ref27=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.createPlatformPayToken)(params);});return function(_x36){return _ref27.apply(this,arguments);};}(),[]);var _updatePlatformPaySheet=(0,_react.useCallback)(function(){var _ref28=(0,_asyncToGenerator2.default)(function*(params){return(0,_functions.updatePlatformPaySheet)(params);});return function(_x37){return _ref28.apply(this,arguments);};}(),[]);var _openPlatformPaySetup=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){return(0,_functions.openPlatformPaySetup)();}),[]);return{retrievePaymentIntent:_retrievePaymentIntent,retrieveSetupIntent:_retrieveSetupIntent,confirmPayment:_confirmPayment,createPaymentMethod:_createPaymentMethod,handleNextAction:_handleNextAction,handleNextActionForSetup:_handleNextActionForSetup,confirmSetupIntent:_confirmSetupIntent,createTokenForCVCUpdate:_createTokenForCVCUpdate,handleURLCallback:_handleURLCallback,confirmPaymentSheetPayment:_confirmPaymentSheetPayment,presentPaymentSheet:_presentPaymentSheet,initPaymentSheet:_initPaymentSheet,createToken:_createToken,collectBankAccountForPayment:_collectBankAccountForPayment,collectBankAccountForSetup:_collectBankAccountForSetup,verifyMicrodepositsForPayment:_verifyMicrodepositsForPayment,verifyMicrodepositsForSetup:_verifyMicrodepositsForSetup,canAddCardToWallet:_canAddCardToWallet,collectBankAccountToken:_collectBankAccountToken,collectFinancialConnectionsAccounts:_collectFinancialConnectionsAccounts,resetPaymentSheetCustomer:_resetPaymentSheetCustomer,isPlatformPaySupported:_isPlatformPaySupported,confirmPlatformPaySetupIntent:_confirmPlatformPaySetupIntent,confirmPlatformPayPayment:_confirmPlatformPayPayment,dismissPlatformPay:_dismissPlatformPay,createPlatformPayPaymentMethod:_createPlatformPayPaymentMethod,createPlatformPayToken:_createPlatformPayToken,updatePlatformPaySheet:_updatePlatformPaySheet,openPlatformPaySetup:_openPlatformPaySetup};}
//# sourceMappingURL=useStripe.js.map