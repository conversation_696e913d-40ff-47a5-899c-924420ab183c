var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.PlatformPayButton=PlatformPayButton;var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireDefault(require("react"));var _reactNative=require("react-native");var _NativeStripeSdkModule=_interopRequireDefault(require("../specs/NativeStripeSdkModule"));var _PlatformPay=require("../types/PlatformPay");var _NativeApplePayButton=_interopRequireDefault(require("../specs/NativeApplePayButton"));var _NativeGooglePayButton=_interopRequireDefault(require("../specs/NativeGooglePayButton"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/PlatformPayButton.tsx";var _excluded=["type","appearance","onPress","disabled","borderRadius","onShippingMethodSelected","onShippingContactSelected","onCouponCodeEntered","setOrderTracking","style"];function PlatformPayButton(_ref){var _ref$type=_ref.type,type=_ref$type===void 0?_PlatformPay.ButtonType.Default:_ref$type,_ref$appearance=_ref.appearance,appearance=_ref$appearance===void 0?_PlatformPay.ButtonStyle.Automatic:_ref$appearance,onPress=_ref.onPress,disabled=_ref.disabled,borderRadius=_ref.borderRadius,onShippingMethodSelected=_ref.onShippingMethodSelected,onShippingContactSelected=_ref.onShippingContactSelected,onCouponCodeEntered=_ref.onCouponCodeEntered,setOrderTracking=_ref.setOrderTracking,style=_ref.style,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);var shippingMethodCallback=onShippingMethodSelected?function(value){onShippingMethodSelected&&onShippingMethodSelected(value.nativeEvent);}:undefined;var shippingContactCallback=onShippingContactSelected?function(value){onShippingContactSelected(value.nativeEvent);}:undefined;var couponCodeCallback=onCouponCodeEntered?function(value){onCouponCodeEntered&&onCouponCodeEntered(value.nativeEvent);}:undefined;var orderTrackingCallback=setOrderTracking?function(){setOrderTracking(_NativeStripeSdkModule.default.configureOrderTracking);}:undefined;return(0,_jsxRuntime.jsx)(_reactNative.TouchableOpacity,{disabled:disabled,activeOpacity:disabled?0.3:1,onPress:onPress,style:[disabled?styles.disabled:styles.notDisabled,style],children:_reactNative.Platform.OS==='ios'?(0,_jsxRuntime.jsx)(_NativeApplePayButton.default,Object.assign({type:type,buttonStyle:appearance,borderRadius:borderRadius,disabled:disabled!=null?disabled:false,onShippingMethodSelectedAction:shippingMethodCallback,onShippingContactSelectedAction:shippingContactCallback,onCouponCodeEnteredAction:couponCodeCallback,onOrderTrackingAction:orderTrackingCallback,style:styles.nativeButtonStyle},props)):(0,_jsxRuntime.jsx)(_NativeGooglePayButton.default,Object.assign({type:type,appearance:appearance,borderRadius:borderRadius,style:styles.nativeButtonStyle},props))});}var styles=_reactNative.StyleSheet.create({disabled:{flex:0,opacity:0.4},notDisabled:{flex:0},nativeButtonStyle:{flex:1}});
//# sourceMappingURL=PlatformPayButton.js.map