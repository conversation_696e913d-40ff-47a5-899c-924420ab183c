var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.StripeContainer=StripeContainer;var _react=_interopRequireDefault(require("react"));var _reactNative=require("react-native");var _NativeStripeContainer=_interopRequireDefault(require("../specs/NativeStripeContainer"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/StripeContainer.tsx";function StripeContainer(_ref){var keyboardShouldPersistTaps=_ref.keyboardShouldPersistTaps,children=_ref.children;return(0,_jsxRuntime.jsx)(_NativeStripeContainer.default,{keyboardShouldPersistTaps:keyboardShouldPersistTaps!=null?keyboardShouldPersistTaps:true,style:styles.container,children:(0,_jsxRuntime.jsx)(_reactNative.View,{style:styles.container,accessible:false,children:children})});}var styles=_reactNative.StyleSheet.create({container:{flex:1}});
//# sourceMappingURL=StripeContainer.js.map