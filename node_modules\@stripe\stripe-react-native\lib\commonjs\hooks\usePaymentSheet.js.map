{"version": 3, "names": ["_react", "require", "_useStripe2", "usePaymentSheet", "_useStripe", "useStripe", "initPaymentSheetNative", "initPaymentSheet", "presentPaymentSheetNative", "presentPaymentSheet", "confirmPaymentSheetPaymentNative", "confirmPaymentSheetPayment", "resetPaymentSheetCustomerNative", "resetPaymentSheetCustomer", "_useState", "useState", "_useState2", "_slicedToArray2", "default", "loading", "setLoading", "useCallback", "_ref", "_asyncToGenerator2", "params", "result", "_x", "apply", "arguments", "_ref2", "options", "_x2"], "sourceRoot": "../../../src", "sources": ["hooks/usePaymentSheet.tsx"], "mappings": "kXAAA,IAAAA,MAAA,CAAAC,OAAA,UACA,IAAAC,WAAA,CAAAD,OAAA,gBAOO,QAAS,CAAAE,eAAeA,CAAA,CAAG,CAChC,IAAAC,UAAA,CAKI,GAAAC,qBAAS,EAAC,CAAC,CAJKC,sBAAsB,CAAAF,UAAA,CAAxCG,gBAAgB,CACKC,yBAAyB,CAAAJ,UAAA,CAA9CK,mBAAmB,CACSC,gCAAgC,CAAAN,UAAA,CAA5DO,0BAA0B,CACCC,+BAA+B,CAAAR,UAAA,CAA1DS,yBAAyB,CAE3B,IAAAC,SAAA,CAA8B,GAAAC,eAAQ,EAAC,KAAK,CAAC,CAAAC,UAAA,IAAAC,eAAA,CAAAC,OAAA,EAAAJ,SAAA,IAAtCK,OAAO,CAAAH,UAAA,IAAEI,UAAU,CAAAJ,UAAA,IAE1B,GAAM,CAAAT,gBAAgB,CAAG,GAAAc,kBAAW,iBAAAC,IAAA,IAAAC,kBAAA,CAAAL,OAAA,EAClC,UAAOM,MAAgC,CAAK,CAC1CJ,UAAU,CAAC,IAAI,CAAC,CAChB,GAAM,CAAAK,MAAM,MAAS,CAAAnB,sBAAsB,CAACkB,MAAM,CAAC,CACnDJ,UAAU,CAAC,KAAK,CAAC,CACjB,MAAO,CAAAK,MAAM,CACf,CAAC,kBAAAC,EAAA,SAAAJ,IAAA,CAAAK,KAAA,MAAAC,SAAA,QACD,CAACtB,sBAAsB,CACzB,CAAC,CAED,GAAM,CAAAG,mBAAmB,CAAG,GAAAY,kBAAW,iBAAAQ,KAAA,IAAAN,kBAAA,CAAAL,OAAA,EACrC,UAAOY,OAAqC,CAAK,CAC/CV,UAAU,CAAC,IAAI,CAAC,CAChB,GAAM,CAAAK,MAAM,MAAS,CAAAjB,yBAAyB,CAACsB,OAAO,CAAC,CACvDV,UAAU,CAAC,KAAK,CAAC,CACjB,MAAO,CAAAK,MAAM,CACf,CAAC,kBAAAM,GAAA,SAAAF,KAAA,CAAAF,KAAA,MAAAC,SAAA,QACD,CAACpB,yBAAyB,CAC5B,CAAC,CAED,GAAM,CAAAG,0BAA0B,CAAG,GAAAU,kBAAW,KAAAE,kBAAA,CAAAL,OAAA,EAAC,WAAY,CACzDE,UAAU,CAAC,IAAI,CAAC,CAChB,GAAM,CAAAK,MAAM,MAAS,CAAAf,gCAAgC,CAAC,CAAC,CACvDU,UAAU,CAAC,KAAK,CAAC,CACjB,MAAO,CAAAK,MAAM,CACf,CAAC,EAAE,CAACf,gCAAgC,CAAC,CAAC,CAEtC,GAAM,CAAAG,yBAAyB,CAAG,GAAAQ,kBAAW,KAAAE,kBAAA,CAAAL,OAAA,EAAC,WAAY,CACxDE,UAAU,CAAC,IAAI,CAAC,CAChB,GAAM,CAAAK,MAAM,MAAS,CAAAb,+BAA+B,CAAC,CAAC,CACtDQ,UAAU,CAAC,KAAK,CAAC,CACjB,MAAO,CAAAK,MAAM,CACf,CAAC,EAAE,CAACb,+BAA+B,CAAC,CAAC,CAErC,MAAO,CACLO,OAAO,CAAPA,OAAO,CACPZ,gBAAgB,CAAhBA,gBAAgB,CAChBE,mBAAmB,CAAnBA,mBAAmB,CACnBE,0BAA0B,CAA1BA,0BAA0B,CAM1BE,yBAAyB,CAAzBA,yBACF,CAAC,CACH", "ignoreList": []}