var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.useConfirmSetupIntent=useConfirmSetupIntent;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _react=require("react");var _useStripe2=require("./useStripe");function useConfirmSetupIntent(){var _useState=(0,_react.useState)(false),_useState2=(0,_slicedToArray2.default)(_useState,2),loading=_useState2[0],setLoading=_useState2[1];var _useStripe=(0,_useStripe2.useStripe)(),confirmSetupIntent=_useStripe.confirmSetupIntent;var _confirmSetupIntent=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,data){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};setLoading(true);var result=yield confirmSetupIntent(paymentIntentClientSecret,data,options);setLoading(false);return result;});return function(_x,_x2){return _ref.apply(this,arguments);};}(),[confirmSetupIntent]);return{confirmSetupIntent:_confirmSetupIntent,loading:loading};}
//# sourceMappingURL=useConfirmSetupIntent.js.map