{"version": 3, "names": ["_codegenNativeComponent", "_interopRequireDefault", "require", "NativeComponentRegistry", "_require", "ConditionallyIgnoredEventHandlers", "nativeComponentName", "__INTERNAL_VIEW_CONFIG", "exports", "uiViewClassName", "directEventTypes", "topSubmitAction", "registrationName", "topErrorAction", "validAttributes", "Object", "assign", "visible", "presentationStyle", "animationStyle", "appearance", "defaultValues", "additionalFields", "allowedCountries", "autocompleteCountries", "primaryButtonTitle", "sheetTitle", "googlePlacesApiKey", "onSubmitAction", "onErrorAction", "_default", "default", "get"], "sourceRoot": "../../../src", "sources": ["specs/NativeAddressSheet.ts"], "mappings": "kMAKA,IAAAA,uBAAA,CAAAC,sBAAA,CAAAC,OAAA,6DA0CA,IAAAC,uBAEmB,CAFnBD,OAEmB,CAFnB,gEAEkB,CAAC,CAFnB,IAAAE,QAAA,CAAAF,OAEmB,CAFnB,yDAEkB,CAAC,CAFnBG,iCAEmB,CAAAD,QAAA,CAFnBC,iCAEmB,CAFnB,IAAAC,mBAEmB,CAFnB,kBAEmB,CAFnB,IAAAC,sBAEmB,CAAAC,OAAA,CAAAD,sBAAA,CAFnB,CAAAE,eAEmB,CAFnB,kBAEmB,CAFnBC,gBAEmB,CAFnB,CAAAC,eAEmB,CAFnB,CAAAC,gBAEmB,CAFnB,gBAEkB,CAAC,CAFnBC,cAEmB,CAFnB,CAAAD,gBAEmB,CAFnB,eAEkB,EAAC,CAFnBE,eAEmB,CAAAC,MAAA,CAAAC,MAAA,EAFnBC,OAEmB,CAFnB,IAEmB,CAFnBC,iBAEmB,CAFnB,IAEmB,CAFnBC,cAEmB,CAFnB,IAEmB,CAFnBC,UAEmB,CAFnB,IAEmB,CAFnBC,aAEmB,CAFnB,IAEmB,CAFnBC,gBAEmB,CAFnB,IAEmB,CAFnBC,gBAEmB,CAFnB,IAEmB,CAFnBC,qBAEmB,CAFnB,IAEmB,CAFnBC,kBAEmB,CAFnB,IAEmB,CAFnBC,UAEmB,CAFnB,IAEmB,CAFnBC,kBAEmB,CAFnB,IAEmB,EAFnBtB,iCAEmB,CAFnB,CAAAuB,cAEmB,CAFnB,IAEmB,CAFnBC,aAEmB,CAFnB,IAEkB,EAAC,CAAD,CAAC,KAAAC,QAAA,CAAAtB,OAAA,CAAAuB,OAAA,CAFnB5B,uBAEmB,CAFnB6B,GAEmB,CAFnB1B,mBAEmB,CAFnB,kBAAAC,sBAEmB,EAAD,CAAC", "ignoreList": []}