{"version": 3, "names": ["_react", "require", "_functions", "useStripe", "_createPaymentMethod", "useCallback", "_ref", "_asyncToGenerator2", "default", "data", "options", "arguments", "length", "undefined", "createPaymentMethod", "_x", "apply", "_createToken", "_ref2", "params", "createToken", "_x2", "_retrievePaymentIntent", "_ref3", "clientSecret", "retrievePaymentIntent", "_x3", "_retrieveSetupIntent", "_ref4", "retrieveSetupIntent", "_x4", "_confirmPayment", "_ref5", "paymentIntentClientSecret", "confirmPayment", "_x5", "_x6", "_handleNextAction", "_ref6", "returnURL", "handleNextAction", "_x7", "_x8", "_handleNextActionForSetup", "_ref7", "setupIntentClientSecret", "handleNextActionForSetup", "_x9", "_x10", "_confirmSetupIntent", "_ref8", "confirmSetupIntent", "_x11", "_x12", "_createTokenForCVCUpdate", "_ref9", "cvc", "createTokenForCVCUpdate", "_x13", "_initPaymentSheet", "_ref10", "initPaymentSheet", "_x14", "_presentPaymentSheet", "_ref11", "presentPaymentSheet", "_x15", "_confirmPaymentSheetPayment", "confirmPaymentSheetPayment", "_handleURLCallback", "_ref13", "url", "handleURLCallback", "_x16", "_collectBankAccountForPayment", "_ref14", "collectBankAccountForPayment", "_x17", "_x18", "_collectBankAccountForSetup", "_ref15", "collectBankAccountForSetup", "_x19", "_x20", "_verifyMicrodepositsForPayment", "_ref16", "verifyMicrodepositsForPayment", "_x21", "_x22", "_verifyMicrodepositsForSetup", "_ref17", "verifyMicrodepositsForSetup", "_x23", "_x24", "_canAddCardToWallet", "_ref18", "canAddCardToWallet", "_x25", "_collectBankAccountToken", "_ref19", "collectBankAccountToken", "_x26", "_x27", "_collectFinancialConnectionsAccounts", "_ref20", "collectFinancialConnectionsAccounts", "_x28", "_x29", "_resetPaymentSheetCustomer", "resetPaymentSheetCustomer", "_isPlatformPaySupported", "_ref22", "isPlatformPaySupported", "_x30", "_confirmPlatformPaySetupIntent", "_ref23", "confirmPlatformPaySetupIntent", "_x31", "_x32", "_confirmPlatformPayPayment", "_ref24", "confirmPlatformPayPayment", "_x33", "_x34", "_dismissPlatformPay", "dismissPlatformPay", "_createPlatformPayPaymentMethod", "_ref26", "createPlatformPayPaymentMethod", "_x35", "_createPlatformPayToken", "_ref27", "createPlatformPayToken", "_x36", "_updatePlatformPaySheet", "_ref28", "updatePlatformPaySheet", "_x37", "_openPlatformPaySetup", "openPlatformPaySetup"], "sourceRoot": "../../../src", "sources": ["hooks/useStripe.tsx"], "mappings": "0QA8BA,IAAAA,MAAA,CAAAC,OAAA,UACA,IAAAC,UAAA,CAAAD,OAAA,iBAqCO,QAAS,CAAAE,SAASA,CAAA,CAAG,CAC1B,GAAM,CAAAC,oBAAoB,CAAG,GAAAC,kBAAW,iBAAAC,IAAA,IAAAC,kBAAA,CAAAC,OAAA,EACtC,UACEC,IAAgC,CAEO,IADvC,CAAAC,OAAoC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAEzC,MAAO,GAAAG,8BAAmB,EAACL,IAAI,CAAEC,OAAO,CAAC,CAC3C,CAAC,kBAAAK,EAAA,SAAAT,IAAA,CAAAU,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAM,YAAY,CAAG,GAAAZ,kBAAW,iBAAAa,KAAA,IAAAX,kBAAA,CAAAC,OAAA,EAC9B,UAAOW,MAA0B,CAAiC,CAChE,MAAO,GAAAC,sBAAW,EAACD,MAAM,CAAC,CAC5B,CAAC,kBAAAE,GAAA,SAAAH,KAAA,CAAAF,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAW,sBAAsB,CAAG,GAAAjB,kBAAW,iBAAAkB,KAAA,IAAAhB,kBAAA,CAAAC,OAAA,EACxC,UAAOgB,YAAoB,CAA2C,CACpE,MAAO,GAAAC,gCAAqB,EAACD,YAAY,CAAC,CAC5C,CAAC,kBAAAE,GAAA,SAAAH,KAAA,CAAAP,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAgB,oBAAoB,CAAG,GAAAtB,kBAAW,iBAAAuB,KAAA,IAAArB,kBAAA,CAAAC,OAAA,EACtC,UAAOgB,YAAoB,CAAyC,CAClE,MAAO,GAAAK,8BAAmB,EAACL,YAAY,CAAC,CAC1C,CAAC,kBAAAM,GAAA,SAAAF,KAAA,CAAAZ,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAoB,eAAe,CAAG,GAAA1B,kBAAW,iBAAA2B,KAAA,IAAAzB,kBAAA,CAAAC,OAAA,EACjC,UACEyB,yBAAiC,CACjCxB,IAAkC,CAEA,IADlC,CAAAC,OAAqC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAE1C,MAAO,GAAAuB,yBAAc,EAACD,yBAAyB,CAAExB,IAAI,CAAEC,OAAO,CAAC,CACjE,CAAC,kBAAAyB,GAAA,CAAAC,GAAA,SAAAJ,KAAA,CAAAhB,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA0B,iBAAiB,CAAG,GAAAhC,kBAAW,iBAAAiC,KAAA,IAAA/B,kBAAA,CAAAC,OAAA,EACnC,UACEyB,yBAAiC,CACjCM,SAAkB,CACkB,CACpC,MAAO,GAAAC,2BAAgB,EAACP,yBAAyB,CAAEM,SAAS,CAAC,CAC/D,CAAC,kBAAAE,GAAA,CAAAC,GAAA,SAAAJ,KAAA,CAAAtB,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAgC,yBAAyB,CAAG,GAAAtC,kBAAW,iBAAAuC,KAAA,IAAArC,kBAAA,CAAAC,OAAA,EAC3C,UACEqC,uBAA+B,CAC/BN,SAAkB,CAC0B,CAC5C,MAAO,GAAAO,mCAAwB,EAACD,uBAAuB,CAAEN,SAAS,CAAC,CACrE,CAAC,kBAAAQ,GAAA,CAAAC,IAAA,SAAAJ,KAAA,CAAA5B,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAsC,mBAAmB,CAAG,GAAA5C,kBAAW,iBAAA6C,KAAA,IAAA3C,kBAAA,CAAAC,OAAA,EACrC,UACEyB,yBAAiC,CACjCxB,IAA+B,CAEO,IADtC,CAAAC,OAAmC,CAAAC,SAAA,CAAAC,MAAA,IAAAD,SAAA,MAAAE,SAAA,CAAAF,SAAA,IAAG,CAAC,CAAC,CAExC,MAAO,GAAAwC,6BAAkB,EAAClB,yBAAyB,CAAExB,IAAI,CAAEC,OAAO,CAAC,CACrE,CAAC,kBAAA0C,IAAA,CAAAC,IAAA,SAAAH,KAAA,CAAAlC,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA2C,wBAAwB,CAAG,GAAAjD,kBAAW,iBAAAkD,KAAA,IAAAhD,kBAAA,CAAAC,OAAA,EAC1C,UAAOgD,GAAW,CAA6C,CAC7D,MAAO,GAAAC,kCAAuB,EAACD,GAAG,CAAC,CACrC,CAAC,kBAAAE,IAAA,SAAAH,KAAA,CAAAvC,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAgD,iBAAiB,CAAG,GAAAtD,kBAAW,iBAAAuD,MAAA,IAAArD,kBAAA,CAAAC,OAAA,EACnC,UACEW,MAAgC,CACI,CACpC,MAAO,GAAA0C,2BAAgB,EAAC1C,MAAM,CAAC,CACjC,CAAC,kBAAA2C,IAAA,SAAAF,MAAA,CAAA5C,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAoD,oBAAoB,CAAG,GAAA1D,kBAAW,iBAAA2D,MAAA,IAAAzD,kBAAA,CAAAC,OAAA,EACtC,UACEE,OAAqC,CACE,CACvC,MAAO,GAAAuD,8BAAmB,EAACvD,OAAO,CAAC,CACrC,CAAC,kBAAAwD,IAAA,SAAAF,MAAA,CAAAhD,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAwD,2BAA2B,CAC/B,GAAA9D,kBAAW,KAAAE,kBAAA,CAAAC,OAAA,EAAC,WAAuD,CACjE,MAAO,GAAA4D,qCAA0B,EAAC,CAAC,CACrC,CAAC,EAAE,EAAE,CAAC,CAER,GAAM,CAAAC,kBAAkB,CAAG,GAAAhE,kBAAW,iBAAAiE,MAAA,IAAA/D,kBAAA,CAAAC,OAAA,EACpC,UAAO+D,GAAW,CAAuB,CACvC,MAAO,GAAAC,4BAAiB,EAACD,GAAG,CAAC,CAC/B,CAAC,kBAAAE,IAAA,SAAAH,MAAA,CAAAtD,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA+D,6BAA6B,CAAG,GAAArE,kBAAW,iBAAAsE,MAAA,IAAApE,kBAAA,CAAAC,OAAA,EAC/C,UACEgB,YAAoB,CACpBL,MAA8C,CACE,CAChD,MAAO,GAAAyD,uCAA4B,EAACpD,YAAY,CAAEL,MAAM,CAAC,CAC3D,CAAC,kBAAA0D,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAA3D,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAoE,2BAA2B,CAAG,GAAA1E,kBAAW,iBAAA2E,MAAA,IAAAzE,kBAAA,CAAAC,OAAA,EAC7C,UACEgB,YAAoB,CACpBL,MAA8C,CACA,CAC9C,MAAO,GAAA8D,qCAA0B,EAACzD,YAAY,CAAEL,MAAM,CAAC,CACzD,CAAC,kBAAA+D,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAhE,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAyE,8BAA8B,CAAG,GAAA/E,kBAAW,iBAAAgF,MAAA,IAAA9E,kBAAA,CAAAC,OAAA,EAChD,UACEgB,YAAoB,CACpBL,MAAiC,CACgB,CACjD,MAAO,GAAAmE,wCAA6B,EAAC9D,YAAY,CAAEL,MAAM,CAAC,CAC5D,CAAC,kBAAAoE,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAArE,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA8E,4BAA4B,CAAG,GAAApF,kBAAW,iBAAAqF,MAAA,IAAAnF,kBAAA,CAAAC,OAAA,EAC9C,UACEgB,YAAoB,CACpBL,MAAiC,CACc,CAC/C,MAAO,GAAAwE,sCAA2B,EAACnE,YAAY,CAAEL,MAAM,CAAC,CAC1D,CAAC,kBAAAyE,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAA1E,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAmF,mBAAmB,CAAG,GAAAzF,kBAAW,iBAAA0F,MAAA,IAAAxF,kBAAA,CAAAC,OAAA,EACrC,UACEW,MAAgC,CACM,CACtC,MAAO,GAAA6E,6BAAkB,EAAC7E,MAAM,CAAC,CACnC,CAAC,kBAAA8E,IAAA,SAAAF,MAAA,CAAA/E,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAuF,wBAAwB,CAAG,GAAA7F,kBAAW,iBAAA8F,MAAA,IAAA5F,kBAAA,CAAAC,OAAA,EAC1C,UACEgB,YAAoB,CACpBL,MAAsC,CACQ,CAC9C,MAAO,GAAAiF,kCAAuB,EAAC5E,YAAY,CAAEL,MAAM,CAAC,CACtD,CAAC,kBAAAkF,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAnF,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA4F,oCAAoC,CAAG,GAAAlG,kBAAW,iBAAAmG,MAAA,IAAAjG,kBAAA,CAAAC,OAAA,EACtD,UACEgB,YAAoB,CACpBL,MAAkD,CACF,CAChD,MAAO,GAAAsF,8CAAmC,EAACjF,YAAY,CAAEL,MAAM,CAAC,CAClE,CAAC,kBAAAuF,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAxF,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAiG,0BAA0B,CAAG,GAAAvG,kBAAW,KAAAE,kBAAA,CAAAC,OAAA,EAAC,WAA2B,CACxE,MAAO,GAAAqG,oCAAyB,EAAC,CAAC,CACpC,CAAC,EAAE,EAAE,CAAC,CAEN,GAAM,CAAAC,uBAAuB,CAAG,GAAAzG,kBAAW,iBAAA0G,MAAA,IAAAxG,kBAAA,CAAAC,OAAA,EACzC,UAAOW,MAEN,CAAuB,CACtB,MAAO,GAAA6F,iCAAsB,EAAC7F,MAAM,CAAC,CACvC,CAAC,kBAAA8F,IAAA,SAAAF,MAAA,CAAA/F,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAuG,8BAA8B,CAAG,GAAA7G,kBAAW,iBAAA8G,MAAA,IAAA5G,kBAAA,CAAAC,OAAA,EAChD,UACEgB,YAAoB,CACpBL,MAAiC,CACiB,CAClD,MAAO,GAAAiG,wCAA6B,EAAC5F,YAAY,CAAEL,MAAM,CAAC,CAC5D,CAAC,kBAAAkG,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAnG,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA4G,0BAA0B,CAAG,GAAAlH,kBAAW,iBAAAmH,MAAA,IAAAjH,kBAAA,CAAAC,OAAA,EAC5C,UACEgB,YAAoB,CACpBL,MAAiC,CACa,CAC9C,MAAO,GAAAsG,oCAAyB,EAACjG,YAAY,CAAEL,MAAM,CAAC,CACxD,CAAC,kBAAAuG,IAAA,CAAAC,IAAA,SAAAH,MAAA,CAAAxG,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAiH,mBAAmB,CAAG,GAAAvH,kBAAW,KAAAE,kBAAA,CAAAC,OAAA,EAAC,WAA8B,CACpE,MAAO,GAAAqH,6BAAkB,EAAC,CAAC,CAC7B,CAAC,EAAE,EAAE,CAAC,CAEN,GAAM,CAAAC,+BAA+B,CAAG,GAAAzH,kBAAW,iBAAA0H,MAAA,IAAAxH,kBAAA,CAAAC,OAAA,EACjD,UACEW,MAAuC,CACM,CAC7C,MAAO,GAAA6G,yCAA8B,EAAC7G,MAAM,CAAC,CAC/C,CAAC,kBAAA8G,IAAA,SAAAF,MAAA,CAAA/G,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAAuH,uBAAuB,CAAG,GAAA7H,kBAAW,iBAAA8H,MAAA,IAAA5H,kBAAA,CAAAC,OAAA,EACzC,UACEW,MAAuC,CACF,CACrC,MAAO,GAAAiH,iCAAsB,EAACjH,MAAM,CAAC,CACvC,CAAC,kBAAAkH,IAAA,SAAAF,MAAA,CAAAnH,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA2H,uBAAuB,CAAG,GAAAjI,kBAAW,iBAAAkI,MAAA,IAAAhI,kBAAA,CAAAC,OAAA,EACzC,UAAOW,MAMN,CAEK,CACJ,MAAO,GAAAqH,iCAAsB,EAACrH,MAAM,CAAC,CACvC,CAAC,kBAAAsH,IAAA,SAAAF,MAAA,CAAAvH,KAAA,MAAAL,SAAA,QACD,EACF,CAAC,CAED,GAAM,CAAA+H,qBAAqB,CAAG,GAAArI,kBAAW,KAAAE,kBAAA,CAAAC,OAAA,EAAC,WAA2B,CACnE,MAAO,GAAAmI,+BAAoB,EAAC,CAAC,CAC/B,CAAC,EAAE,EAAE,CAAC,CAEN,MAAO,CACLlH,qBAAqB,CAAEH,sBAAsB,CAC7CO,mBAAmB,CAAEF,oBAAoB,CACzCO,cAAc,CAAEH,eAAe,CAC/BjB,mBAAmB,CAAEV,oBAAoB,CACzCoC,gBAAgB,CAAEH,iBAAiB,CACnCS,wBAAwB,CAAEH,yBAAyB,CACnDQ,kBAAkB,CAAEF,mBAAmB,CACvCQ,uBAAuB,CAAEH,wBAAwB,CACjDkB,iBAAiB,CAAEH,kBAAkB,CACrCD,0BAA0B,CAAED,2BAA2B,CACvDF,mBAAmB,CAAEF,oBAAoB,CACzCF,gBAAgB,CAAEF,iBAAiB,CACnCvC,WAAW,CAAEH,YAAY,CACzB2D,4BAA4B,CAAEF,6BAA6B,CAC3DO,0BAA0B,CAAEF,2BAA2B,CACvDO,6BAA6B,CAAEF,8BAA8B,CAC7DO,2BAA2B,CAAEF,4BAA4B,CACzDO,kBAAkB,CAAEF,mBAAmB,CACvCM,uBAAuB,CAAEF,wBAAwB,CACjDO,mCAAmC,CAAEF,oCAAoC,CAMzEM,yBAAyB,CAAED,0BAA0B,CACrDI,sBAAsB,CAAEF,uBAAuB,CAC/CM,6BAA6B,CAAEF,8BAA8B,CAC7DO,yBAAyB,CAAEF,0BAA0B,CACrDM,kBAAkB,CAAED,mBAAmB,CACvCI,8BAA8B,CAAEF,+BAA+B,CAC/DM,sBAAsB,CAAEF,uBAAuB,CAC/CM,sBAAsB,CAAEF,uBAAuB,CAC/CK,oBAAoB,CAAED,qBACxB,CAAC,CACH", "ignoreList": []}