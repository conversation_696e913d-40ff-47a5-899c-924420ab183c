{"version": 3, "file": "useStripe.d.ts", "sourceRoot": "", "sources": ["../../../../src/hooks/useStripe.tsx"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACV,aAAa,EACb,aAAa,EACb,YAAY,EACZ,yBAAyB,EACzB,2BAA2B,EAC3B,yBAAyB,EACzB,oBAAoB,EACpB,sBAAsB,EACtB,8BAA8B,EAC9B,wBAAwB,EACxB,6BAA6B,EAC7B,WAAW,EACX,sBAAsB,EACtB,yBAAyB,EACzB,gCAAgC,EAChC,WAAW,EACX,iBAAiB,EACjB,KAAK,EACL,yBAAyB,EACzB,mCAAmC,EACnC,iCAAiC,EACjC,gCAAgC,EAChC,kCAAkC,EAClC,wBAAwB,EACxB,wBAAwB,EACxB,oBAAoB,EACpB,WAAW,EACX,gBAAgB,EACjB,MAAM,UAAU,CAAC;AAiClB,OAAO,KAAK,EAAE,6BAA6B,EAAE,MAAM,wBAAwB,CAAC;AAC5E,OAAO,KAAK,EAAE,yCAAyC,EAAE,MAAM,+BAA+B,CAAC;AAE/F;;GAEG;AACH,wBAAgB,SAAS;0CAmBA,MAAM,KAAG,OAAO,CAAC,2BAA2B,CAAC;wCAO7C,MAAM,KAAG,OAAO,CAAC,yBAAyB,CAAC;gDAQnC,MAAM,SAC1B,aAAa,CAAC,aAAa,YACzB,aAAa,CAAC,cAAc,KACpC,OAAO,CAAC,oBAAoB,CAAC;gCAlCxB,aAAa,CAAC,YAAY,YACvB,aAAa,CAAC,aAAa,KACnC,OAAO,CAAC,yBAAyB,CAAC;kDAwCR,MAAM,cACrB,MAAM,KACjB,OAAO,CAAC,sBAAsB,CAAC;wDAQP,MAAM,cACnB,MAAM,KACjB,OAAO,CAAC,8BAA8B,CAAC;oDAQb,MAAM,QAC3B,WAAW,CAAC,aAAa,YACtB,WAAW,CAAC,cAAc,KAClC,OAAO,CAAC,wBAAwB,CAAC;mCAOxB,MAAM,KAAG,OAAO,CAAC,6BAA6B,CAAC;6BA8B/C,MAAM,KAAG,OAAO,CAAC,OAAO,CAAC;sCALf,OAAO,CAAC,gCAAgC,CAAC;oCARnD,YAAY,CAAC,cAAc,KACpC,OAAO,CAAC,yBAAyB,CAAC;+BAV3B,YAAY,CAAC,WAAW,KAC/B,OAAO,CAAC,sBAAsB,CAAC;0BAxEnB,KAAK,CAAC,YAAY,KAAG,OAAO,CAAC,iBAAiB,CAAC;iDAqG9C,MAAM,UACZ,aAAa,CAAC,wBAAwB,KAC7C,OAAO,CAAC,kCAAkC,CAAC;+CAQ9B,MAAM,UACZ,aAAa,CAAC,wBAAwB,KAC7C,OAAO,CAAC,gCAAgC,CAAC;kDAQ5B,MAAM,UACZ,yBAAyB,KAChC,OAAO,CAAC,mCAAmC,CAAC;gDAQ/B,MAAM,UACZ,yBAAyB,KAChC,OAAO,CAAC,iCAAiC,CAAC;iCAQnC,wBAAwB,KAC/B,OAAO,CAAC,wBAAwB,CAAC;4CAQpB,MAAM,WACX,6BAA6B,KACrC,OAAO,CAAC,oBAAoB,CAAC,WAAW,CAAC;wDAQ5B,MAAM,WACX,yCAAyC,KACjD,OAAO,CAAC,oBAAoB,CAAC,aAAa,CAAC;IAqG9C;;;;OAIG;qCAnGoD,OAAO,CAAC,IAAI,CAAC;sCAKpD;QACd,SAAS,CAAC,EAAE,WAAW,CAAC,0BAA0B,CAAC;KACpD,KAAG,OAAO,CAAC,OAAO,CAAC;kDAQJ,MAAM,UACZ,WAAW,CAAC,aAAa,KAChC,OAAO,CAAC,WAAW,CAAC,wBAAwB,CAAC;8CAQhC,MAAM,UACZ,WAAW,CAAC,aAAa,KAChC,OAAO,CAAC,WAAW,CAAC,oBAAoB,CAAC;8BAMI,OAAO,CAAC,OAAO,CAAC;6CAMtD,WAAW,CAAC,mBAAmB,KACtC,OAAO,CAAC,WAAW,CAAC,mBAAmB,CAAC;qCAQjC,WAAW,CAAC,mBAAmB,KACtC,OAAO,CAAC,WAAW,CAAC,WAAW,CAAC;qCAOpB;QACb,QAAQ,EAAE;YACR,SAAS,EAAE,KAAK,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;YAC9C,eAAe,EAAE,KAAK,CAAC,WAAW,CAAC,cAAc,CAAC,CAAC;YACnD,MAAM,EAAE,KAAK,CAAC,WAAW,CAAC,kBAAkB,CAAC,CAAC;SAC/C,CAAC;KACH,KAAG,OAAO,CAAC;QACV,KAAK,CAAC,EAAE,WAAW,CAAC,gBAAgB,CAAC,CAAC;KACvC,CAAC;gCAMgD,OAAO,CAAC,IAAI,CAAC;EAwClE"}