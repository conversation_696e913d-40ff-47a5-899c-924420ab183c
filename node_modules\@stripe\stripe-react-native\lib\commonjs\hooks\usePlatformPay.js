var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.usePlatformPay=usePlatformPay;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _react=require("react");var _useStripe2=require("./useStripe");function usePlatformPay(){var _useStripe=(0,_useStripe2.useStripe)(),isPlatformPaySupported=_useStripe.isPlatformPaySupported,confirmPlatformPaySetupIntent=_useStripe.confirmPlatformPaySetupIntent,confirmPlatformPayPayment=_useStripe.confirmPlatformPayPayment,createPlatformPayPaymentMethod=_useStripe.createPlatformPayPaymentMethod,createPlatformPayToken=_useStripe.createPlatformPayToken,dismissPlatformPay=_useStripe.dismissPlatformPay,updatePlatformPaySheet=_useStripe.updatePlatformPaySheet,canAddCardToWallet=_useStripe.canAddCardToWallet,openPlatformPaySetup=_useStripe.openPlatformPaySetup;var _useState=(0,_react.useState)(false),_useState2=(0,_slicedToArray2.default)(_useState,2),loading=_useState2[0],setLoading=_useState2[1];var _isPlatformPaySupported=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield isPlatformPaySupported(params);setLoading(false);return result;});return function(_x){return _ref.apply(this,arguments);};}(),[isPlatformPaySupported]);var _confirmPlatformPaySetupIntent=(0,_react.useCallback)(function(){var _ref2=(0,_asyncToGenerator2.default)(function*(clientSecret,params){setLoading(true);var result=yield confirmPlatformPaySetupIntent(clientSecret,params);setLoading(false);return result;});return function(_x2,_x3){return _ref2.apply(this,arguments);};}(),[confirmPlatformPaySetupIntent]);var _confirmPlatformPayPayment=(0,_react.useCallback)(function(){var _ref3=(0,_asyncToGenerator2.default)(function*(clientSecret,params){setLoading(true);var result=yield confirmPlatformPayPayment(clientSecret,params);setLoading(false);return result;});return function(_x4,_x5){return _ref3.apply(this,arguments);};}(),[confirmPlatformPayPayment]);var _createPlatformPayPaymentMethod=(0,_react.useCallback)(function(){var _ref4=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield createPlatformPayPaymentMethod(params);setLoading(false);return result;});return function(_x6){return _ref4.apply(this,arguments);};}(),[createPlatformPayPaymentMethod]);var _createPlatformPayToken=(0,_react.useCallback)(function(){var _ref5=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield createPlatformPayToken(params);setLoading(false);return result;});return function(_x7){return _ref5.apply(this,arguments);};}(),[createPlatformPayToken]);var _dismissPlatformPay=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){setLoading(true);var result=yield dismissPlatformPay();setLoading(false);return result;}),[dismissPlatformPay]);var _updatePlatformPaySheet=(0,_react.useCallback)(function(){var _ref7=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield updatePlatformPaySheet(params);setLoading(false);return result;});return function(_x8){return _ref7.apply(this,arguments);};}(),[updatePlatformPaySheet]);var _canAddCardToWallet=(0,_react.useCallback)(function(){var _ref8=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield canAddCardToWallet(params);setLoading(false);return result;});return function(_x9){return _ref8.apply(this,arguments);};}(),[canAddCardToWallet]);var _openPlatformPaySetup=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){return openPlatformPaySetup();}),[openPlatformPaySetup]);return{loading:loading,isPlatformPaySupported:_isPlatformPaySupported,confirmPlatformPaySetupIntent:_confirmPlatformPaySetupIntent,confirmPlatformPayPayment:_confirmPlatformPayPayment,createPlatformPayPaymentMethod:_createPlatformPayPaymentMethod,createPlatformPayToken:_createPlatformPayToken,dismissPlatformPay:_dismissPlatformPay,updatePlatformPaySheet:_updatePlatformPaySheet,canAddCardToWallet:_canAddCardToWallet,openPlatformPaySetup:_openPlatformPaySetup};}
//# sourceMappingURL=usePlatformPay.js.map