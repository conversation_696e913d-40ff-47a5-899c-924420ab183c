var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=void 0;var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var nativeComponentName='StripeContainer';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'StripeContainer',validAttributes:{keyboardShouldPersistTaps:true}};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});
//# sourceMappingURL=NativeStripeContainer.js.map