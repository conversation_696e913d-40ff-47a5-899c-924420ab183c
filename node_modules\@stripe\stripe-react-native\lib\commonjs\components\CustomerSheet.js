var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.CustomerSheet=void 0;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _react=_interopRequireDefault(require("react"));var _NativeStripeSdkModule=_interopRequireDefault(require("../specs/NativeStripeSdkModule"));var _events=require("../events");var fetchPaymentMethodsCallback=null;var attachPaymentMethodCallback=null;var detachPaymentMethodCallback=null;var setSelectedPaymentOptionCallback=null;var fetchSelectedPaymentOptionCallback=null;var setupIntentClientSecretForCustomerAttachCallback=null;var initialize=function(){var _ref=(0,_asyncToGenerator2.default)(function*(params){var customerAdapterOverrides={};if(params.customerAdapter){customerAdapterOverrides=configureCustomerAdapterEventListeners(params.customerAdapter);}try{var _yield$NativeStripeSd=yield _NativeStripeSdkModule.default.initCustomerSheet(params,customerAdapterOverrides),error=_yield$NativeStripeSd.error;if(error){return{error:error};}return{};}catch(error){return{error:error};}});return function initialize(_x){return _ref.apply(this,arguments);};}();var configureCustomerAdapterEventListeners=function configureCustomerAdapterEventListeners(customerAdapter){if(customerAdapter.fetchPaymentMethods){var _fetchPaymentMethodsC;(_fetchPaymentMethodsC=fetchPaymentMethodsCallback)==null||_fetchPaymentMethodsC.remove();fetchPaymentMethodsCallback=(0,_events.addListener)('onCustomerAdapterFetchPaymentMethodsCallback',(0,_asyncToGenerator2.default)(function*(){if(customerAdapter.fetchPaymentMethods){var paymentMethods=yield customerAdapter.fetchPaymentMethods();yield _NativeStripeSdkModule.default.customerAdapterFetchPaymentMethodsCallback(paymentMethods);}else{throw new Error('[@stripe/stripe-react-native] Tried to call `fetchPaymentMethods` on your CustomerAdapter, but no matching method was found.');}}));}if(customerAdapter.attachPaymentMethod){var _attachPaymentMethodC;(_attachPaymentMethodC=attachPaymentMethodCallback)==null||_attachPaymentMethodC.remove();attachPaymentMethodCallback=(0,_events.addListener)('onCustomerAdapterAttachPaymentMethodCallback',function(){var _ref4=(0,_asyncToGenerator2.default)(function*(_ref3){var paymentMethodId=_ref3.paymentMethodId;if(customerAdapter.attachPaymentMethod){var paymentMethod=yield customerAdapter.attachPaymentMethod(paymentMethodId);yield _NativeStripeSdkModule.default.customerAdapterAttachPaymentMethodCallback(paymentMethod);}else{throw new Error('[@stripe/stripe-react-native] Tried to call `attachPaymentMethod` on your CustomerAdapter, but no matching method was found.');}});return function(_x2){return _ref4.apply(this,arguments);};}());}if(customerAdapter.detachPaymentMethod){var _detachPaymentMethodC;(_detachPaymentMethodC=detachPaymentMethodCallback)==null||_detachPaymentMethodC.remove();detachPaymentMethodCallback=(0,_events.addListener)('onCustomerAdapterDetachPaymentMethodCallback',function(){var _ref6=(0,_asyncToGenerator2.default)(function*(_ref5){var paymentMethodId=_ref5.paymentMethodId;if(customerAdapter.detachPaymentMethod){var paymentMethod=yield customerAdapter.detachPaymentMethod(paymentMethodId);yield _NativeStripeSdkModule.default.customerAdapterDetachPaymentMethodCallback(paymentMethod);}else{throw new Error('[@stripe/stripe-react-native] Tried to call `detachPaymentMethod` on your CustomerAdapter, but no matching method was found.');}});return function(_x3){return _ref6.apply(this,arguments);};}());}if(customerAdapter.setSelectedPaymentOption){var _setSelectedPaymentOp;(_setSelectedPaymentOp=setSelectedPaymentOptionCallback)==null||_setSelectedPaymentOp.remove();setSelectedPaymentOptionCallback=(0,_events.addListener)('onCustomerAdapterSetSelectedPaymentOptionCallback',function(){var _ref8=(0,_asyncToGenerator2.default)(function*(_ref7){var paymentOption=_ref7.paymentOption;if(customerAdapter.setSelectedPaymentOption){yield customerAdapter.setSelectedPaymentOption(paymentOption);yield _NativeStripeSdkModule.default.customerAdapterSetSelectedPaymentOptionCallback();}else{throw new Error('[@stripe/stripe-react-native] Tried to call `setSelectedPaymentOption` on your CustomerAdapter, but no matching method was found.');}});return function(_x4){return _ref8.apply(this,arguments);};}());}if(customerAdapter.fetchSelectedPaymentOption){var _fetchSelectedPayment;(_fetchSelectedPayment=fetchSelectedPaymentOptionCallback)==null||_fetchSelectedPayment.remove();fetchSelectedPaymentOptionCallback=(0,_events.addListener)('onCustomerAdapterFetchSelectedPaymentOptionCallback',(0,_asyncToGenerator2.default)(function*(){if(customerAdapter.fetchSelectedPaymentOption){var paymentOption=yield customerAdapter.fetchSelectedPaymentOption();yield _NativeStripeSdkModule.default.customerAdapterFetchSelectedPaymentOptionCallback(paymentOption);}else{throw new Error('[@stripe/stripe-react-native] Tried to call `fetchSelectedPaymentOption` on your CustomerAdapter, but no matching method was found.');}}));}if(customerAdapter.setupIntentClientSecretForCustomerAttach){var _setupIntentClientSec;(_setupIntentClientSec=setupIntentClientSecretForCustomerAttachCallback)==null||_setupIntentClientSec.remove();setupIntentClientSecretForCustomerAttachCallback=(0,_events.addListener)('onCustomerAdapterSetupIntentClientSecretForCustomerAttachCallback',(0,_asyncToGenerator2.default)(function*(){if(customerAdapter.setupIntentClientSecretForCustomerAttach){var clientSecret=yield customerAdapter.setupIntentClientSecretForCustomerAttach();yield _NativeStripeSdkModule.default.customerAdapterSetupIntentClientSecretForCustomerAttachCallback(clientSecret);}else{throw new Error('[@stripe/stripe-react-native] Tried to call `setupIntentClientSecretForCustomerAttach` on your CustomerAdapter, but no matching method was found.');}}));}return{fetchPaymentMethods:!!customerAdapter.fetchPaymentMethods,attachPaymentMethod:!!customerAdapter.attachPaymentMethod,detachPaymentMethod:!!customerAdapter.detachPaymentMethod,setSelectedPaymentOption:!!customerAdapter.setSelectedPaymentOption,fetchSelectedPaymentOption:!!customerAdapter.fetchSelectedPaymentOption,setupIntentClientSecretForCustomerAttach:!!customerAdapter.setupIntentClientSecretForCustomerAttach};};var present=function(){var _ref11=(0,_asyncToGenerator2.default)(function*(){var params=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{return yield _NativeStripeSdkModule.default.presentCustomerSheet(params);}catch(error){return{error:error};}});return function present(){return _ref11.apply(this,arguments);};}();var retrievePaymentOptionSelection=function(){var _ref12=(0,_asyncToGenerator2.default)(function*(){try{return yield _NativeStripeSdkModule.default.retrieveCustomerSheetPaymentOptionSelection();}catch(error){return{error:error};}});return function retrievePaymentOptionSelection(){return _ref12.apply(this,arguments);};}();function Component(_ref13){var visible=_ref13.visible,presentationStyle=_ref13.presentationStyle,animationStyle=_ref13.animationStyle,style=_ref13.style,appearance=_ref13.appearance,setupIntentClientSecret=_ref13.setupIntentClientSecret,customerId=_ref13.customerId,customerEphemeralKeySecret=_ref13.customerEphemeralKeySecret,merchantDisplayName=_ref13.merchantDisplayName,headerTextForSelectionScreen=_ref13.headerTextForSelectionScreen,defaultBillingDetails=_ref13.defaultBillingDetails,billingDetailsCollectionConfiguration=_ref13.billingDetailsCollectionConfiguration,returnURL=_ref13.returnURL,removeSavedPaymentMethodMessage=_ref13.removeSavedPaymentMethodMessage,applePayEnabled=_ref13.applePayEnabled,googlePayEnabled=_ref13.googlePayEnabled,timeout=_ref13.timeout,onResult=_ref13.onResult,customerAdapter=_ref13.customerAdapter;_react.default.useEffect(function(){if(visible){initialize({style:style,appearance:appearance,setupIntentClientSecret:setupIntentClientSecret,customerId:customerId,customerEphemeralKeySecret:customerEphemeralKeySecret,merchantDisplayName:merchantDisplayName,headerTextForSelectionScreen:headerTextForSelectionScreen,defaultBillingDetails:defaultBillingDetails,billingDetailsCollectionConfiguration:billingDetailsCollectionConfiguration,returnURL:returnURL,removeSavedPaymentMethodMessage:removeSavedPaymentMethodMessage,applePayEnabled:applePayEnabled,googlePayEnabled:googlePayEnabled,customerAdapter:customerAdapter}).then(function(initResult){if(initResult.error){onResult(initResult);}else{present({timeout:timeout,presentationStyle:presentationStyle,animationStyle:animationStyle}).then(function(presentResult){onResult(presentResult);});}});}},[visible]);return null;}var CustomerSheet=exports.CustomerSheet={Component:Component,initialize:initialize,present:present,retrievePaymentOptionSelection:retrievePaymentOptionSelection};
//# sourceMappingURL=CustomerSheet.js.map