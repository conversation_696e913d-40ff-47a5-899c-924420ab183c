var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.usePaymentSheet=usePaymentSheet;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _slicedToArray2=_interopRequireDefault(require("@babel/runtime/helpers/slicedToArray"));var _react=require("react");var _useStripe2=require("./useStripe");function usePaymentSheet(){var _useStripe=(0,_useStripe2.useStripe)(),initPaymentSheetNative=_useStripe.initPaymentSheet,presentPaymentSheetNative=_useStripe.presentPaymentSheet,confirmPaymentSheetPaymentNative=_useStripe.confirmPaymentSheetPayment,resetPaymentSheetCustomerNative=_useStripe.resetPaymentSheetCustomer;var _useState=(0,_react.useState)(false),_useState2=(0,_slicedToArray2.default)(_useState,2),loading=_useState2[0],setLoading=_useState2[1];var initPaymentSheet=(0,_react.useCallback)(function(){var _ref=(0,_asyncToGenerator2.default)(function*(params){setLoading(true);var result=yield initPaymentSheetNative(params);setLoading(false);return result;});return function(_x){return _ref.apply(this,arguments);};}(),[initPaymentSheetNative]);var presentPaymentSheet=(0,_react.useCallback)(function(){var _ref2=(0,_asyncToGenerator2.default)(function*(options){setLoading(true);var result=yield presentPaymentSheetNative(options);setLoading(false);return result;});return function(_x2){return _ref2.apply(this,arguments);};}(),[presentPaymentSheetNative]);var confirmPaymentSheetPayment=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){setLoading(true);var result=yield confirmPaymentSheetPaymentNative();setLoading(false);return result;}),[confirmPaymentSheetPaymentNative]);var resetPaymentSheetCustomer=(0,_react.useCallback)((0,_asyncToGenerator2.default)(function*(){setLoading(true);var result=yield resetPaymentSheetCustomerNative();setLoading(false);return result;}),[resetPaymentSheetCustomerNative]);return{loading:loading,initPaymentSheet:initPaymentSheet,presentPaymentSheet:presentPaymentSheet,confirmPaymentSheetPayment:confirmPaymentSheetPayment,resetPaymentSheetCustomer:resetPaymentSheetCustomer};}
//# sourceMappingURL=usePaymentSheet.js.map