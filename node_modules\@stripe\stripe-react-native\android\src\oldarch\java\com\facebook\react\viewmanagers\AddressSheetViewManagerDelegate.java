/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.DynamicFromObject;
import com.facebook.react.bridge.ReadableArray;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class AddressSheetViewManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & AddressSheetViewManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public AddressSheetViewManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "visible":
        mViewManager.setVisible(view, value == null ? false : (boolean) value);
        break;
      case "presentationStyle":
        mViewManager.setPresentationStyle(view, value == null ? "popover" : (String) value);
        break;
      case "animationStyle":
        mViewManager.setAnimationStyle(view, value == null ? "slide" : (String) value);
        break;
      case "appearance":
        mViewManager.setAppearance(view, new DynamicFromObject(value));
        break;
      case "defaultValues":
        mViewManager.setDefaultValues(view, new DynamicFromObject(value));
        break;
      case "additionalFields":
        mViewManager.setAdditionalFields(view, new DynamicFromObject(value));
        break;
      case "allowedCountries":
        mViewManager.setAllowedCountries(view, (ReadableArray) value);
        break;
      case "autocompleteCountries":
        mViewManager.setAutocompleteCountries(view, (ReadableArray) value);
        break;
      case "primaryButtonTitle":
        mViewManager.setPrimaryButtonTitle(view, value == null ? null : (String) value);
        break;
      case "sheetTitle":
        mViewManager.setSheetTitle(view, value == null ? null : (String) value);
        break;
      case "googlePlacesApiKey":
        mViewManager.setGooglePlacesApiKey(view, value == null ? null : (String) value);
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
