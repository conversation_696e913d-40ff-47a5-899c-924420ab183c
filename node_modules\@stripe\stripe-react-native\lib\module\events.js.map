{"version": 3, "names": ["_reactNative", "require", "_NativeStripeSdkModule", "_interopRequireDefault", "compatEventEmitter", "NativeStripeSdkModule", "onConfirmHandlerCallback", "Platform", "OS", "NativeEventEmitter", "DeviceEventEmitter", "addListener", "event", "handler"], "sourceRoot": "../../src", "sources": ["events.ts"], "mappings": "4KAQA,IAAAA,YAAA,CAAAC,OAAA,iBAMA,IAAAC,sBAAA,CAAAC,sBAAA,CAAAF,OAAA,mCAEA,GAAM,CAAAG,kBAAkB,CAGtBC,8BAAqB,CAACC,wBAAwB,EAAI,IAAI,CAClDC,qBAAQ,CAACC,EAAE,GAAK,KAAK,CACnB,GAAI,CAAAC,+BAAkB,CAACJ,8BAA4B,CAAC,CACpDK,+BAAkB,CACpB,IAAI,CAiBH,QAAS,CAAAC,WAAWA,CACzBC,KAAa,CACbC,OAA8D,CAC3C,CACnB,GAAIT,kBAAkB,EAAI,IAAI,CAAE,CAC9B,MAAO,CAAAA,kBAAkB,CAACO,WAAW,CAACC,KAAK,CAAEC,OAAO,CAAC,CACvD,CACA,MAAO,CAAAR,8BAAqB,CAACO,KAAK,CAAC,CAACC,OAAc,CAAC,CACrD", "ignoreList": []}