/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaInterface.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.Dynamic;
import com.facebook.react.bridge.ReadableArray;

public interface AddressSheetViewManagerInterface<T extends View>  {
  void setVisible(T view, boolean value);
  void setPresentationStyle(T view, @Nullable String value);
  void setAnimationStyle(T view, @Nullable String value);
  void setAppearance(T view, Dynamic value);
  void setDefaultValues(T view, Dynamic value);
  void setAdditionalFields(T view, Dynamic value);
  void setAllowedCountries(T view, @Nullable ReadableArray value);
  void setAutocompleteCountries(T view, @Nullable ReadableArray value);
  void setPrimaryButtonTitle(T view, @Nullable String value);
  void setSheetTitle(T view, @Nullable String value);
  void setGooglePlacesApiKey(T view, @Nullable String value);
}
