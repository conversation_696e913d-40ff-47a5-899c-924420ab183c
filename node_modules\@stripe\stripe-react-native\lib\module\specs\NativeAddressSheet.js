var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=void 0;var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var _require=require('react-native/Libraries/NativeComponent/ViewConfigIgnore'),ConditionallyIgnoredEventHandlers=_require.ConditionallyIgnoredEventHandlers;var nativeComponentName='AddressSheetView';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'AddressSheetView',directEventTypes:{topSubmitAction:{registrationName:'onSubmitAction'},topErrorAction:{registrationName:'onErrorAction'}},validAttributes:Object.assign({visible:true,presentationStyle:true,animationStyle:true,appearance:true,defaultValues:true,additionalFields:true,allowedCountries:true,autocompleteCountries:true,primaryButtonTitle:true,sheetTitle:true,googlePlacesApiKey:true},ConditionallyIgnoredEventHandlers({onSubmitAction:true,onErrorAction:true}))};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});
//# sourceMappingURL=NativeAddressSheet.js.map