/**
 * Compatibility helper to use new arch events if available and fallback
 * to NativeEventEmitter or DeviceEventEmitter.
 *
 * Can be removed once we no longer need to support the old arch and use
 * the methods on NativeStripeSdkModule directly.
 */
import { EventSubscription } from 'react-native';
import NativeStripeSdkModule from './specs/NativeStripeSdkModule';
type Events = 'onConfirmHandlerCallback' | 'onFinancialConnectionsEvent' | 'onOrderTrackingCallback' | 'onCustomerAdapterFetchPaymentMethodsCallback' | 'onCustomerAdapterAttachPaymentMethodCallback' | 'onCustomerAdapterDetachPaymentMethodCallback' | 'onCustomerAdapterSetSelectedPaymentOptionCallback' | 'onCustomerAdapterFetchSelectedPaymentOptionCallback' | 'onCustomerAdapterSetupIntentClientSecretForCustomerAttachCallback' | 'embeddedPaymentElementFormSheetConfirmComplete' | 'embeddedPaymentElementDidUpdatePaymentOption' | 'embeddedPaymentElementDidUpdateHeight' | 'embeddedPaymentElementLoadingFailed';
export declare function addListener<EventT extends Events>(event: EventT, handler: Parameters<(typeof NativeStripeSdkModule)[EventT]>[0]): EventSubscription;
export {};
//# sourceMappingURL=events.d.ts.map