export type CartSummaryItem = DeferredCartSummaryItem | ImmediateCartSummaryItem | RecurringCartSummaryItem;
export type CartSummaryItemType = 'Deferred' | 'Immediate' | 'Recurring';
/** Use this type for a payment that occurs in the future, such as a pre-order. Only available on iOS 15 and up, otherwise falls back to ImmediateCartSummaryItem. */
export type DeferredCartSummaryItem = {
    paymentType: 'Deferred';
    /** The unix timestamp of the date, in the future, of the payment. Measured in seconds. */
    deferredDate: number;
    label: string;
    amount: string;
};
/** Use this type for payments that will occur immediately. */
export type ImmediateCartSummaryItem = {
    paymentType: 'Immediate';
    /** When creating items for estimates or charges whose final value is not yet known, set this to true. */
    isPending?: boolean;
    label: string;
    amount: string;
};
/** Use this type for payments that occur more than once, such as a subscription. Only available on iOS 15 and up, otherwise falls back to ImmediateCartSummaryItem.*/
export type RecurringCartSummaryItem = {
    paymentType: 'Recurring';
    /** The amount of time – in calendar units such as day, month, or year – that represents a fraction of the total payment interval. For example, if you set the intervalUnit to 'month' and intervalCount to 3, then the payment interval is three months.*/
    intervalUnit: 'minute' | 'hour' | 'day' | 'month' | 'year';
    /** The number of interval units that make up the total payment interval. For example, if you set the intervalUnit to 'month' and intervalCount to 3, then the payment interval is three months.*/
    intervalCount: number;
    /** The unix timestamp of the start date. Measured in seconds. */
    startDate?: number;
    /** The unix timestamp of the end date. Measured in seconds. */
    endDate?: number;
    label: string;
    amount: string;
};
//# sourceMappingURL=ApplePay.d.ts.map