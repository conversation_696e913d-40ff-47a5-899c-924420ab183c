{"version": 3, "names": ["_react", "require", "_useStripe2", "usePlatformPay", "_useStripe", "useStripe", "isPlatformPaySupported", "confirmPlatformPaySetupIntent", "confirmPlatformPayPayment", "createPlatformPayPaymentMethod", "createPlatformPayToken", "dismissPlatformPay", "updatePlatformPaySheet", "canAddCardToWallet", "openPlatformPaySetup", "_useState", "useState", "_useState2", "_slicedToArray2", "default", "loading", "setLoading", "_isPlatformPaySupported", "useCallback", "_ref", "_asyncToGenerator2", "params", "result", "_x", "apply", "arguments", "_confirmPlatformPaySetupIntent", "_ref2", "clientSecret", "_x2", "_x3", "_confirmPlatformPayPayment", "_ref3", "_x4", "_x5", "_createPlatformPayPaymentMethod", "_ref4", "_x6", "_createPlatformPayToken", "_ref5", "_x7", "_dismissPlatformPay", "_updatePlatformPaySheet", "_ref7", "_x8", "_canAddCardToWallet", "_ref8", "_x9", "_openPlatformPaySetup"], "sourceRoot": "../../../src", "sources": ["hooks/usePlatformPay.tsx"], "mappings": "gXAAA,IAAAA,MAAA,CAAAC,OAAA,UAMA,IAAAC,WAAA,CAAAD,OAAA,gBAKO,QAAS,CAAAE,cAAcA,CAAA,CAAG,CAC/B,IAAAC,UAAA,CAUI,GAAAC,qBAAS,EAAC,CAAC,CATbC,sBAAsB,CAAAF,UAAA,CAAtBE,sBAAsB,CACtBC,6BAA6B,CAAAH,UAAA,CAA7BG,6BAA6B,CAC7BC,yBAAyB,CAAAJ,UAAA,CAAzBI,yBAAyB,CACzBC,8BAA8B,CAAAL,UAAA,CAA9BK,8BAA8B,CAC9BC,sBAAsB,CAAAN,UAAA,CAAtBM,sBAAsB,CACtBC,kBAAkB,CAAAP,UAAA,CAAlBO,kBAAkB,CAClBC,sBAAsB,CAAAR,UAAA,CAAtBQ,sBAAsB,CACtBC,kBAAkB,CAAAT,UAAA,CAAlBS,kBAAkB,CAClBC,oBAAoB,CAAAV,UAAA,CAApBU,oBAAoB,CAEtB,IAAAC,SAAA,CAA8B,GAAAC,eAAQ,EAAC,KAAK,CAAC,CAAAC,UAAA,IAAAC,eAAA,CAAAC,OAAA,EAAAJ,SAAA,IAAtCK,OAAO,CAAAH,UAAA,IAAEI,UAAU,CAAAJ,UAAA,IAE1B,GAAM,CAAAK,uBAAuB,CAAG,GAAAC,kBAAW,iBAAAC,IAAA,IAAAC,kBAAA,CAAAN,OAAA,EACzC,UAAOO,MAA+D,CAAK,CACzEL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAArB,sBAAsB,CAACoB,MAAM,CAAC,CACnDL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAC,EAAA,SAAAJ,IAAA,CAAAK,KAAA,MAAAC,SAAA,QACD,CAACxB,sBAAsB,CACzB,CAAC,CAED,GAAM,CAAAyB,8BAA8B,CAAG,GAAAR,kBAAW,iBAAAS,KAAA,IAAAP,kBAAA,CAAAN,OAAA,EAChD,UAAOc,YAAoB,CAAEP,MAAiC,CAAK,CACjEL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAApB,6BAA6B,CAAC0B,YAAY,CAAEP,MAAM,CAAC,CACxEL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAO,GAAA,CAAAC,GAAA,SAAAH,KAAA,CAAAH,KAAA,MAAAC,SAAA,QACD,CAACvB,6BAA6B,CAChC,CAAC,CAED,GAAM,CAAA6B,0BAA0B,CAAG,GAAAb,kBAAW,iBAAAc,KAAA,IAAAZ,kBAAA,CAAAN,OAAA,EAC5C,UAAOc,YAAoB,CAAEP,MAAiC,CAAK,CACjEL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAnB,yBAAyB,CAACyB,YAAY,CAAEP,MAAM,CAAC,CACpEL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAW,GAAA,CAAAC,GAAA,SAAAF,KAAA,CAAAR,KAAA,MAAAC,SAAA,QACD,CAACtB,yBAAyB,CAC5B,CAAC,CAED,GAAM,CAAAgC,+BAA+B,CAAG,GAAAjB,kBAAW,iBAAAkB,KAAA,IAAAhB,kBAAA,CAAAN,OAAA,EACjD,UAAOO,MAAuC,CAAK,CACjDL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAlB,8BAA8B,CAACiB,MAAM,CAAC,CAC3DL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAe,GAAA,SAAAD,KAAA,CAAAZ,KAAA,MAAAC,SAAA,QACD,CAACrB,8BAA8B,CACjC,CAAC,CAED,GAAM,CAAAkC,uBAAuB,CAAG,GAAApB,kBAAW,iBAAAqB,KAAA,IAAAnB,kBAAA,CAAAN,OAAA,EACzC,UAAOO,MAAuC,CAAK,CACjDL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAjB,sBAAsB,CAACgB,MAAM,CAAC,CACnDL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAkB,GAAA,SAAAD,KAAA,CAAAf,KAAA,MAAAC,SAAA,QACD,CAACpB,sBAAsB,CACzB,CAAC,CAED,GAAM,CAAAoC,mBAAmB,CAAG,GAAAvB,kBAAW,KAAAE,kBAAA,CAAAN,OAAA,EAAC,WAAY,CAClDE,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAhB,kBAAkB,CAAC,CAAC,CACzCU,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,EAAE,CAAChB,kBAAkB,CAAC,CAAC,CAExB,GAAM,CAAAoC,uBAAuB,CAAG,GAAAxB,kBAAW,iBAAAyB,KAAA,IAAAvB,kBAAA,CAAAN,OAAA,EACzC,UAAOO,MAMN,CAAK,CACJL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAf,sBAAsB,CAACc,MAAM,CAAC,CACnDL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAsB,GAAA,SAAAD,KAAA,CAAAnB,KAAA,MAAAC,SAAA,QACD,CAAClB,sBAAsB,CACzB,CAAC,CAED,GAAM,CAAAsC,mBAAmB,CAAG,GAAA3B,kBAAW,iBAAA4B,KAAA,IAAA1B,kBAAA,CAAAN,OAAA,EACrC,UACEO,MAAgC,CACM,CACtCL,UAAU,CAAC,IAAI,CAAC,CAEhB,GAAM,CAAAM,MAAM,MAAS,CAAAd,kBAAkB,CAACa,MAAM,CAAC,CAC/CL,UAAU,CAAC,KAAK,CAAC,CAEjB,MAAO,CAAAM,MAAM,CACf,CAAC,kBAAAyB,GAAA,SAAAD,KAAA,CAAAtB,KAAA,MAAAC,SAAA,QACD,CAACjB,kBAAkB,CACrB,CAAC,CAED,GAAM,CAAAwC,qBAAqB,CAAG,GAAA9B,kBAAW,KAAAE,kBAAA,CAAAN,OAAA,EAAC,WAA2B,CACnE,MAAO,CAAAL,oBAAoB,CAAC,CAAC,CAC/B,CAAC,EAAE,CAACA,oBAAoB,CAAC,CAAC,CAE1B,MAAO,CAELM,OAAO,CAAPA,OAAO,CAKPd,sBAAsB,CAAEgB,uBAAuB,CAO/Cf,6BAA6B,CAAEwB,8BAA8B,CAO7DvB,yBAAyB,CAAE4B,0BAA0B,CAMrD3B,8BAA8B,CAAE+B,+BAA+B,CAM/D9B,sBAAsB,CAAEiC,uBAAuB,CAK/ChC,kBAAkB,CAAEmC,mBAAmB,CASvClC,sBAAsB,CAAEmC,uBAAuB,CAO/ClC,kBAAkB,CAAEqC,mBAAmB,CAMvCpC,oBAAoB,CAAEuC,qBACxB,CAAC,CACH", "ignoreList": []}