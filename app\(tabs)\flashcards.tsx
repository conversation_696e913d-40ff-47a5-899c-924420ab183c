import { supabase } from "@/lib/supabase";
import { useUser } from "@clerk/clerk-expo";
import { router } from "expo-router";
import React, { useEffect, useState } from "react";
import {
  FlatList,
  RefreshControl,
  StyleSheet,
  Text,
  TouchableOpacity,
  View,
} from "react-native";

interface Flashcard {
  id: string;
  front: string;
  back: string;
  category: string;
  difficulty: "easy" | "medium" | "hard";
  next_review_date: string;
  review_count: number;
  course_id: string;
}

export default function FlashcardsScreen() {
  const { user } = useUser();
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [dueCards, setDueCards] = useState<Flashcard[]>([]);
  const [refreshing, setRefreshing] = useState(false);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState<"all" | "due">("due");
  const [searchQuery, setSearchQuery] = useState("");
  const [filteredFlashcards, setFilteredFlashcards] = useState<Flashcard[]>([]);
  const [selectionMode, setSelectionMode] = useState(false);
  const [selectedCards, setSelectedCards] = useState<Set<string>>(new Set());

  useEffect(() => {
    if (user) {
      loadFlashcards();
    }
  }, [user]);

  useEffect(() => {
    filterFlashcards();
  }, [flashcards, searchQuery]);

  const filterFlashcards = () => {
    let filtered = flashcards;

    if (searchQuery.trim()) {
      filtered = filtered.filter(
        (card) =>
          card.front.toLowerCase().includes(searchQuery.toLowerCase()) ||
          card.back.toLowerCase().includes(searchQuery.toLowerCase()) ||
          (card.category &&
            card.category.toLowerCase().includes(searchQuery.toLowerCase()))
      );
    }

    setFilteredFlashcards(filtered);
  };

  const loadFlashcards = async () => {
    try {
      // Get user from Supabase
      const { data: supabaseUser } = await supabase
        .from("users")
        .select("id")
        .eq("clerk_user_id", user?.id)
        .single();

      if (!supabaseUser) return;

      // Load all flashcards
      const { data: allCards, error: allError } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .order("created_at", { ascending: false });

      if (allError) {
        console.error("Error loading flashcards:", allError);
        return;
      }

      setFlashcards(allCards || []);

      // Load due flashcards
      const { data: dueCardsData, error: dueError } = await supabase
        .from("flashcards")
        .select("*")
        .eq("user_id", supabaseUser.id)
        .lte("next_review_date", new Date().toISOString())
        .order("next_review_date", { ascending: true });

      if (dueError) {
        console.error("Error loading due flashcards:", dueError);
        return;
      }

      setDueCards(dueCardsData || []);
    } catch (error) {
      console.error("Error loading flashcards:", error);
    } finally {
      setLoading(false);
    }
  };

  const onRefresh = async () => {
    setRefreshing(true);
    await loadFlashcards();
    setRefreshing(false);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case "easy":
        return "#34C759";
      case "medium":
        return "#FF9500";
      case "hard":
        return "#FF3B30";
      default:
        return "#007AFF";
    }
  };

  const toggleCardSelection = (cardId: string) => {
    const newSelected = new Set(selectedCards);
    if (newSelected.has(cardId)) {
      newSelected.delete(cardId);
    } else {
      newSelected.add(cardId);
    }
    setSelectedCards(newSelected);
  };

  const selectAllCards = () => {
    const currentData = activeTab === "all" ? filteredFlashcards : dueCards;
    setSelectedCards(new Set(currentData.map((card) => card.id)));
  };

  const clearSelection = () => {
    setSelectedCards(new Set());
    setSelectionMode(false);
  };

  const bulkDeleteCards = async () => {
    if (selectedCards.size === 0) return;

    Alert.alert(
      "Delete Cards",
      `Are you sure you want to delete ${selectedCards.size} flashcard(s)? This action cannot be undone.`,
      [
        { text: "Cancel", style: "cancel" },
        {
          text: "Delete",
          style: "destructive",
          onPress: async () => {
            try {
              const { error } = await supabase
                .from("flashcards")
                .delete()
                .in("id", Array.from(selectedCards));

              if (error) throw error;

              await loadFlashcards();
              clearSelection();
              Alert.alert(
                "Success",
                `${selectedCards.size} flashcard(s) deleted successfully`
              );
            } catch (error) {
              console.error("Error deleting flashcards:", error);
              Alert.alert("Error", "Failed to delete flashcards");
            }
          },
        },
      ]
    );
  };

  const exportCards = async () => {
    if (selectedCards.size === 0) {
      Alert.alert("No Selection", "Please select cards to export");
      return;
    }

    const cardsToExport = flashcards.filter((card) =>
      selectedCards.has(card.id)
    );
    const exportData = cardsToExport.map((card) => ({
      front: card.front,
      back: card.back,
      category: card.category,
      difficulty: card.difficulty,
    }));

    // For now, just show the data in an alert (in a real app, you'd save to file)
    Alert.alert(
      "Export Data",
      `${selectedCards.size} cards ready for export. In a full implementation, this would save to a file.`,
      [{ text: "OK", onPress: clearSelection }]
    );
  };

  const renderFlashcardItem = ({ item }: { item: Flashcard }) => {
    const isSelected = selectedCards.has(item.id);

    return (
      <TouchableOpacity
        style={[
          styles.flashcardCard,
          isSelected && styles.selectedCard,
          selectionMode && styles.selectionModeCard,
        ]}
        onPress={() => {
          if (selectionMode) {
            toggleCardSelection(item.id);
          } else {
            router.push(`/flashcard/${item.id}`);
          }
        }}
        onLongPress={() => {
          if (!selectionMode) {
            setSelectionMode(true);
            toggleCardSelection(item.id);
          }
        }}
      >
        {selectionMode && (
          <View style={styles.selectionIndicator}>
            <View style={[styles.checkbox, isSelected && styles.checkedBox]}>
              {isSelected && <Text style={styles.checkmark}>✓</Text>}
            </View>
          </View>
        )}

        <View style={styles.cardHeader}>
          <View style={styles.cardInfo}>
            {item.category && (
              <Text style={styles.category}>{item.category}</Text>
            )}
            <View
              style={[
                styles.difficultyBadge,
                { backgroundColor: getDifficultyColor(item.difficulty) },
              ]}
            >
              <Text style={styles.difficultyText}>
                {item.difficulty.toUpperCase()}
              </Text>
            </View>
          </View>
          <Text style={styles.reviewCount}>
            Reviewed {item.review_count} times
          </Text>
        </View>

        <Text style={styles.cardFront} numberOfLines={2}>
          {item.front}
        </Text>

        <View style={styles.cardFooter}>
          <Text style={styles.nextReview}>
            Next review: {new Date(item.next_review_date).toLocaleDateString()}
          </Text>
          {new Date(item.next_review_date) <= new Date() && (
            <View style={styles.dueBadge}>
              <Text style={styles.dueText}>DUE</Text>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.centerContainer}>
        <Text style={styles.loadingText}>Loading flashcards...</Text>
      </View>
    );
  }

  const currentData =
    activeTab === "all"
      ? searchQuery
        ? filteredFlashcards
        : flashcards
      : dueCards.filter(
          (card) =>
            !searchQuery ||
            card.front.toLowerCase().includes(searchQuery.toLowerCase()) ||
            card.back.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (card.category &&
              card.category.toLowerCase().includes(searchQuery.toLowerCase()))
        );

  return (
    <View style={styles.container}>
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Flashcards</Text>
        <View style={styles.headerButtons}>
          {!selectionMode && (
            <>
              <TouchableOpacity
                style={styles.createButton}
                onPress={() => router.push("/create-flashcard")}
              >
                <Text style={styles.createButtonText}>+ Create</Text>
              </TouchableOpacity>
              {dueCards.length > 0 && (
                <>
                  <TouchableOpacity
                    style={styles.reviewButton}
                    onPress={() => router.push("/flashcard-review")}
                  >
                    <Text style={styles.reviewButtonText}>Review Now</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={styles.offlineButton}
                    onPress={() => router.push("/offline-review")}
                  >
                    <Text style={styles.offlineButtonText}>📱 Offline</Text>
                  </TouchableOpacity>
                </>
              )}
            </>
          )}
          {selectionMode && (
            <TouchableOpacity
              style={styles.cancelButton}
              onPress={clearSelection}
            >
              <Text style={styles.cancelButtonText}>Cancel</Text>
            </TouchableOpacity>
          )}
        </View>
      </View>

      <View style={styles.searchContainer}>
        <TextInput
          style={styles.searchInput}
          placeholder="Search flashcards..."
          value={searchQuery}
          onChangeText={setSearchQuery}
          placeholderTextColor="#999"
        />
      </View>

      {selectionMode && (
        <View style={styles.bulkActionsContainer}>
          <Text style={styles.selectionCount}>
            {selectedCards.size} card(s) selected
          </Text>
          <View style={styles.bulkActions}>
            <TouchableOpacity
              style={styles.bulkActionButton}
              onPress={selectAllCards}
            >
              <Text style={styles.bulkActionText}>Select All</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.bulkActionButton}
              onPress={exportCards}
            >
              <Text style={styles.bulkActionText}>Export</Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[styles.bulkActionButton, styles.deleteButton]}
              onPress={bulkDeleteCards}
            >
              <Text style={[styles.bulkActionText, styles.deleteButtonText]}>
                Delete
              </Text>
            </TouchableOpacity>
          </View>
        </View>
      )}

      <View style={styles.tabContainer}>
        <TouchableOpacity
          style={[styles.tab, activeTab === "due" && styles.activeTab]}
          onPress={() => setActiveTab("due")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "due" && styles.activeTabText,
            ]}
          >
            Due ({dueCards.length})
          </Text>
        </TouchableOpacity>
        <TouchableOpacity
          style={[styles.tab, activeTab === "all" && styles.activeTab]}
          onPress={() => setActiveTab("all")}
        >
          <Text
            style={[
              styles.tabText,
              activeTab === "all" && styles.activeTabText,
            ]}
          >
            All ({flashcards.length})
          </Text>
        </TouchableOpacity>
      </View>

      {currentData.length === 0 ? (
        <View style={styles.emptyContainer}>
          <Text style={styles.emptyTitle}>
            {activeTab === "due"
              ? "No cards due for review"
              : "No flashcards yet"}
          </Text>
          <Text style={styles.emptyDescription}>
            {activeTab === "due"
              ? "Great job! All your flashcards are up to date."
              : "Upload a course to automatically generate flashcards or create them manually."}
          </Text>
          {activeTab === "all" && (
            <TouchableOpacity
              style={styles.uploadButton}
              onPress={() => router.push("/upload-course")}
            >
              <Text style={styles.uploadButtonText}>Upload Course</Text>
            </TouchableOpacity>
          )}
        </View>
      ) : (
        <FlatList
          data={currentData}
          renderItem={renderFlashcardItem}
          keyExtractor={(item) => item.id}
          contentContainerStyle={styles.listContainer}
          refreshControl={
            <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
          }
          showsVerticalScrollIndicator={false}
        />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    padding: 20,
    paddingTop: 60,
    backgroundColor: "#fff",
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  headerTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  headerButtons: {
    flexDirection: "row",
    alignItems: "center",
  },
  createButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
    marginRight: 10,
  },
  createButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  reviewButton: {
    backgroundColor: "#34C759",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
  },
  reviewButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  offlineButton: {
    backgroundColor: "#FF9500",
    paddingHorizontal: 12,
    paddingVertical: 8,
    borderRadius: 20,
    marginLeft: 8,
  },
  offlineButtonText: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "600",
  },
  tabContainer: {
    flexDirection: "row",
    backgroundColor: "#fff",
    paddingHorizontal: 20,
    paddingBottom: 10,
  },
  tab: {
    flex: 1,
    paddingVertical: 10,
    alignItems: "center",
    borderBottomWidth: 2,
    borderBottomColor: "transparent",
  },
  activeTab: {
    borderBottomColor: "#007AFF",
  },
  tabText: {
    fontSize: 16,
    color: "#666",
  },
  activeTabText: {
    color: "#007AFF",
    fontWeight: "600",
  },
  centerContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  loadingText: {
    fontSize: 16,
    color: "#666",
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    paddingHorizontal: 40,
  },
  emptyTitle: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
    marginBottom: 10,
    textAlign: "center",
  },
  emptyDescription: {
    fontSize: 16,
    color: "#666",
    textAlign: "center",
    marginBottom: 30,
    lineHeight: 24,
  },
  uploadButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 8,
  },
  uploadButtonText: {
    color: "#fff",
    fontSize: 16,
    fontWeight: "600",
  },
  listContainer: {
    padding: 20,
  },
  flashcardCard: {
    backgroundColor: "#fff",
    borderRadius: 12,
    padding: 20,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "flex-start",
    marginBottom: 10,
  },
  cardInfo: {
    flexDirection: "row",
    alignItems: "center",
    flex: 1,
  },
  category: {
    fontSize: 12,
    color: "#666",
    marginRight: 10,
    backgroundColor: "#f0f0f0",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 8,
  },
  difficultyBadge: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  difficultyText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },
  reviewCount: {
    fontSize: 12,
    color: "#999",
  },
  cardFront: {
    fontSize: 16,
    color: "#333",
    lineHeight: 22,
    marginBottom: 15,
  },
  cardFooter: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  nextReview: {
    fontSize: 12,
    color: "#666",
  },
  dueBadge: {
    backgroundColor: "#FF3B30",
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  dueText: {
    color: "#fff",
    fontSize: 10,
    fontWeight: "bold",
  },
  cancelButton: {
    backgroundColor: "#FF3B30",
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  cancelButtonText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  searchContainer: {
    backgroundColor: "#fff",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  searchInput: {
    backgroundColor: "#f8f9fa",
    borderRadius: 8,
    paddingHorizontal: 15,
    paddingVertical: 10,
    fontSize: 16,
    color: "#333",
  },
  bulkActionsContainer: {
    backgroundColor: "#f8f9fa",
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderBottomWidth: 1,
    borderBottomColor: "#e0e0e0",
  },
  selectionCount: {
    fontSize: 14,
    color: "#666",
    marginBottom: 10,
  },
  bulkActions: {
    flexDirection: "row",
    justifyContent: "space-around",
  },
  bulkActionButton: {
    backgroundColor: "#007AFF",
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 6,
  },
  bulkActionText: {
    color: "#fff",
    fontSize: 14,
    fontWeight: "600",
  },
  deleteButton: {
    backgroundColor: "#FF3B30",
  },
  deleteButtonText: {
    color: "#fff",
  },
  selectedCard: {
    borderColor: "#007AFF",
    borderWidth: 2,
  },
  selectionModeCard: {
    paddingLeft: 50,
  },
  selectionIndicator: {
    position: "absolute",
    left: 15,
    top: 15,
    zIndex: 1,
  },
  checkbox: {
    width: 20,
    height: 20,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: "#ccc",
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#fff",
  },
  checkedBox: {
    backgroundColor: "#007AFF",
    borderColor: "#007AFF",
  },
  checkmark: {
    color: "#fff",
    fontSize: 12,
    fontWeight: "bold",
  },
});
