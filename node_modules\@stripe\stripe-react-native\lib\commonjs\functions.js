var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.verifyMicrodepositsForSetup=exports.verifyMicrodepositsForPayment=exports.updatePlatformPaySheet=exports.retrieveSetupIntent=exports.retrievePaymentIntent=exports.resetPaymentSheetCustomer=exports.presentPaymentSheet=exports.openPlatformPaySetup=exports.isPlatformPaySupported=exports.isCardInWallet=exports.initPaymentSheet=exports.handleURLCallback=exports.handleNextActionForSetup=exports.handleNextAction=exports.dismissPlatformPay=exports.createTokenForCVCUpdate=exports.createToken=exports.createPlatformPayToken=exports.createPlatformPayPaymentMethod=exports.createPaymentMethod=exports.confirmSetupIntent=exports.confirmPlatformPaySetupIntent=exports.confirmPlatformPayPayment=exports.confirmPaymentSheetPayment=exports.confirmPayment=exports.collectFinancialConnectionsAccounts=exports.collectBankAccountToken=exports.collectBankAccountForSetup=exports.collectBankAccountForPayment=exports.canAddCardToWallet=exports.Constants=void 0;var _asyncToGenerator2=_interopRequireDefault(require("@babel/runtime/helpers/asyncToGenerator"));var _helpers=require("./helpers");var _Errors=require("./types/Errors");var _NativeStripeSdkModule=_interopRequireDefault(require("./specs/NativeStripeSdkModule"));var _reactNative=require("react-native");var _events=require("./events");var createPaymentMethod=exports.createPaymentMethod=function(){var _ref=(0,_asyncToGenerator2.default)(function*(params){var options=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};try{var _yield$NativeStripeSd=yield _NativeStripeSdkModule.default.createPaymentMethod(params,options),paymentMethod=_yield$NativeStripeSd.paymentMethod,error=_yield$NativeStripeSd.error;if(error){return{error:error};}return{paymentMethod:paymentMethod};}catch(error){return{error:error};}});return function createPaymentMethod(_x){return _ref.apply(this,arguments);};}();var createToken=exports.createToken=function(){var _ref2=(0,_asyncToGenerator2.default)(function*(params){var _params$country;if(params.type==='BankAccount'&&((_params$country=params.country)==null?void 0:_params$country.toLowerCase())==='us'&&!params.routingNumber){return{error:_Errors.MissingRoutingNumber};}try{var _yield$NativeStripeSd2=yield _NativeStripeSdkModule.default.createToken(params),token=_yield$NativeStripeSd2.token,error=_yield$NativeStripeSd2.error;if(error){return{error:error};}return{token:token};}catch(error){return{error:error};}});return function createToken(_x2){return _ref2.apply(this,arguments);};}();var retrievePaymentIntent=exports.retrievePaymentIntent=function(){var _ref3=(0,_asyncToGenerator2.default)(function*(clientSecret){try{var _yield$NativeStripeSd3=yield _NativeStripeSdkModule.default.retrievePaymentIntent(clientSecret),paymentIntent=_yield$NativeStripeSd3.paymentIntent,error=_yield$NativeStripeSd3.error;if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){return{error:error};}});return function retrievePaymentIntent(_x3){return _ref3.apply(this,arguments);};}();var retrieveSetupIntent=exports.retrieveSetupIntent=function(){var _ref4=(0,_asyncToGenerator2.default)(function*(clientSecret){try{var _yield$NativeStripeSd4=yield _NativeStripeSdkModule.default.retrieveSetupIntent(clientSecret),setupIntent=_yield$NativeStripeSd4.setupIntent,error=_yield$NativeStripeSd4.error;if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){return{error:error};}});return function retrieveSetupIntent(_x4){return _ref4.apply(this,arguments);};}();var confirmPayment=exports.confirmPayment=function(){var _ref5=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,params){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};try{var _yield$NativeStripeSd5=yield _NativeStripeSdkModule.default.confirmPayment(paymentIntentClientSecret,params,options),paymentIntent=_yield$NativeStripeSd5.paymentIntent,error=_yield$NativeStripeSd5.error;if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){return{error:error};}});return function confirmPayment(_x5,_x6){return _ref5.apply(this,arguments);};}();var handleNextAction=exports.handleNextAction=function(){var _ref6=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,returnURL){try{var _ref7=_reactNative.Platform.OS==='ios'?yield _NativeStripeSdkModule.default.handleNextAction(paymentIntentClientSecret,returnURL!=null?returnURL:null):yield _NativeStripeSdkModule.default.handleNextAction(paymentIntentClientSecret),paymentIntent=_ref7.paymentIntent,error=_ref7.error;if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function handleNextAction(_x7,_x8){return _ref6.apply(this,arguments);};}();var handleNextActionForSetup=exports.handleNextActionForSetup=function(){var _ref8=(0,_asyncToGenerator2.default)(function*(setupIntentClientSecret,returnURL){try{var _ref9=_reactNative.Platform.OS==='ios'?yield _NativeStripeSdkModule.default.handleNextActionForSetup(setupIntentClientSecret,returnURL!=null?returnURL:null):yield _NativeStripeSdkModule.default.handleNextActionForSetup(setupIntentClientSecret),setupIntent=_ref9.setupIntent,error=_ref9.error;if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function handleNextActionForSetup(_x9,_x10){return _ref8.apply(this,arguments);};}();var confirmSetupIntent=exports.confirmSetupIntent=function(){var _ref10=(0,_asyncToGenerator2.default)(function*(paymentIntentClientSecret,params){var options=arguments.length>2&&arguments[2]!==undefined?arguments[2]:{};try{var _yield$NativeStripeSd6=yield _NativeStripeSdkModule.default.confirmSetupIntent(paymentIntentClientSecret,params,options),setupIntent=_yield$NativeStripeSd6.setupIntent,error=_yield$NativeStripeSd6.error;if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){return{error:error};}});return function confirmSetupIntent(_x11,_x12){return _ref10.apply(this,arguments);};}();var createTokenForCVCUpdate=exports.createTokenForCVCUpdate=function(){var _ref11=(0,_asyncToGenerator2.default)(function*(cvc){try{var _yield$NativeStripeSd7=yield _NativeStripeSdkModule.default.createTokenForCVCUpdate(cvc),tokenId=_yield$NativeStripeSd7.tokenId,error=_yield$NativeStripeSd7.error;if(error){return{error:error};}return{tokenId:tokenId};}catch(error){return{error:error};}});return function createTokenForCVCUpdate(_x13){return _ref11.apply(this,arguments);};}();var handleURLCallback=exports.handleURLCallback=function(){var _ref12=(0,_asyncToGenerator2.default)(function*(url){var stripeHandled=_reactNative.Platform.OS==='ios'?yield _NativeStripeSdkModule.default.handleURLCallback(url):false;return stripeHandled;});return function handleURLCallback(_x14){return _ref12.apply(this,arguments);};}();var verifyMicrodepositsForPayment=exports.verifyMicrodepositsForPayment=function(){var _ref13=(0,_asyncToGenerator2.default)(function*(clientSecret,params){try{var _ref14=yield _NativeStripeSdkModule.default.verifyMicrodeposits(true,clientSecret,params),paymentIntent=_ref14.paymentIntent,error=_ref14.error;if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function verifyMicrodepositsForPayment(_x15,_x16){return _ref13.apply(this,arguments);};}();var verifyMicrodepositsForSetup=exports.verifyMicrodepositsForSetup=function(){var _ref15=(0,_asyncToGenerator2.default)(function*(clientSecret,params){try{var _ref16=yield _NativeStripeSdkModule.default.verifyMicrodeposits(false,clientSecret,params),setupIntent=_ref16.setupIntent,error=_ref16.error;if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function verifyMicrodepositsForSetup(_x17,_x18){return _ref15.apply(this,arguments);};}();var confirmHandlerCallback=null;var orderTrackingCallbackListener=null;var financialConnectionsEventListener=null;var initPaymentSheet=exports.initPaymentSheet=function(){var _ref17=(0,_asyncToGenerator2.default)(function*(params){var _params$intentConfigu,_params$applePay;var result;var confirmHandler=params==null||(_params$intentConfigu=params.intentConfiguration)==null?void 0:_params$intentConfigu.confirmHandler;if(confirmHandler){var _confirmHandlerCallba;(_confirmHandlerCallba=confirmHandlerCallback)==null||_confirmHandlerCallba.remove();confirmHandlerCallback=(0,_events.addListener)('onConfirmHandlerCallback',function(_ref18){var paymentMethod=_ref18.paymentMethod,shouldSavePaymentMethod=_ref18.shouldSavePaymentMethod;confirmHandler(paymentMethod,shouldSavePaymentMethod,_NativeStripeSdkModule.default.intentCreationCallback);});}var orderTrackingCallback=params==null||(_params$applePay=params.applePay)==null?void 0:_params$applePay.setOrderTracking;if(orderTrackingCallback){var _orderTrackingCallbac;(_orderTrackingCallbac=orderTrackingCallbackListener)==null||_orderTrackingCallbac.remove();orderTrackingCallbackListener=(0,_events.addListener)('onOrderTrackingCallback',function(){orderTrackingCallback(_NativeStripeSdkModule.default.configureOrderTracking);});}try{if(_reactNative.Platform.OS==='ios'&&!params.returnURL){console.warn(`[@stripe/stripe-react-native] You have not provided the 'returnURL' field to 'initPaymentSheet', so payment methods that require redirects will not be shown in your iOS Payment Sheet. Visit https://stripe.com/docs/payments/accept-a-payment?platform=react-native&ui=payment-sheet#react-native-set-up-return-url to learn more.`);}result=yield _NativeStripeSdkModule.default.initPaymentSheet(params);if(result.error){return{error:result.error};}return{paymentOption:result.paymentOption};}catch(error){return{error:error};}});return function initPaymentSheet(_x19){return _ref17.apply(this,arguments);};}();var presentPaymentSheet=exports.presentPaymentSheet=function(){var _ref19=(0,_asyncToGenerator2.default)(function*(){var options=arguments.length>0&&arguments[0]!==undefined?arguments[0]:{};try{var _yield$NativeStripeSd8=yield _NativeStripeSdkModule.default.presentPaymentSheet(options),paymentOption=_yield$NativeStripeSd8.paymentOption,error=_yield$NativeStripeSd8.error;if(error){return{error:error};}return{paymentOption:paymentOption};}catch(error){return{error:error};}});return function presentPaymentSheet(){return _ref19.apply(this,arguments);};}();var confirmPaymentSheetPayment=exports.confirmPaymentSheetPayment=function(){var _ref20=(0,_asyncToGenerator2.default)(function*(){try{var _yield$NativeStripeSd9=yield _NativeStripeSdkModule.default.confirmPaymentSheetPayment(),error=_yield$NativeStripeSd9.error;if(error){return{error:error};}return{};}catch(error){return{error:error};}});return function confirmPaymentSheetPayment(){return _ref20.apply(this,arguments);};}();var resetPaymentSheetCustomer=exports.resetPaymentSheetCustomer=function(){var _ref21=(0,_asyncToGenerator2.default)(function*(){return yield _NativeStripeSdkModule.default.resetPaymentSheetCustomer();});return function resetPaymentSheetCustomer(){return _ref21.apply(this,arguments);};}();var collectBankAccountForPayment=exports.collectBankAccountForPayment=function(){var _ref22=(0,_asyncToGenerator2.default)(function*(clientSecret,params){var _financialConnections;(_financialConnections=financialConnectionsEventListener)==null||_financialConnections.remove();if(params.onEvent){financialConnectionsEventListener=(0,_events.addListener)('onFinancialConnectionsEvent',params.onEvent);}try{var _financialConnections2;var _ref23=yield _NativeStripeSdkModule.default.collectBankAccount(true,clientSecret,params),paymentIntent=_ref23.paymentIntent,error=_ref23.error;(_financialConnections2=financialConnectionsEventListener)==null||_financialConnections2.remove();if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){var _financialConnections3;(_financialConnections3=financialConnectionsEventListener)==null||_financialConnections3.remove();return{error:(0,_helpers.createError)(error)};}});return function collectBankAccountForPayment(_x20,_x21){return _ref22.apply(this,arguments);};}();var collectBankAccountForSetup=exports.collectBankAccountForSetup=function(){var _ref24=(0,_asyncToGenerator2.default)(function*(clientSecret,params){var _financialConnections4;(_financialConnections4=financialConnectionsEventListener)==null||_financialConnections4.remove();if(params.onEvent){financialConnectionsEventListener=(0,_events.addListener)('onFinancialConnectionsEvent',params.onEvent);}try{var _financialConnections5;var _ref25=yield _NativeStripeSdkModule.default.collectBankAccount(false,clientSecret,params),setupIntent=_ref25.setupIntent,error=_ref25.error;(_financialConnections5=financialConnectionsEventListener)==null||_financialConnections5.remove();if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){var _financialConnections6;(_financialConnections6=financialConnectionsEventListener)==null||_financialConnections6.remove();return{error:(0,_helpers.createError)(error)};}});return function collectBankAccountForSetup(_x22,_x23){return _ref24.apply(this,arguments);};}();var collectBankAccountToken=exports.collectBankAccountToken=function(){var _ref26=(0,_asyncToGenerator2.default)(function*(clientSecret){var _financialConnections7;var params=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};(_financialConnections7=financialConnectionsEventListener)==null||_financialConnections7.remove();if(params.onEvent){financialConnectionsEventListener=(0,_events.addListener)('onFinancialConnectionsEvent',params.onEvent);}try{var _financialConnections8;var _yield$NativeStripeSd10=yield _NativeStripeSdkModule.default.collectBankAccountToken(clientSecret,params),session=_yield$NativeStripeSd10.session,token=_yield$NativeStripeSd10.token,error=_yield$NativeStripeSd10.error;(_financialConnections8=financialConnectionsEventListener)==null||_financialConnections8.remove();if(error){return{error:error};}return{session:session,token:token};}catch(error){var _financialConnections9;(_financialConnections9=financialConnectionsEventListener)==null||_financialConnections9.remove();return{error:(0,_helpers.createError)(error)};}});return function collectBankAccountToken(_x24){return _ref26.apply(this,arguments);};}();var collectFinancialConnectionsAccounts=exports.collectFinancialConnectionsAccounts=function(){var _ref27=(0,_asyncToGenerator2.default)(function*(clientSecret){var _financialConnections10;var params=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};(_financialConnections10=financialConnectionsEventListener)==null||_financialConnections10.remove();if(params.onEvent){financialConnectionsEventListener=(0,_events.addListener)('onFinancialConnectionsEvent',params.onEvent);}try{var _financialConnections11;var _yield$NativeStripeSd11=yield _NativeStripeSdkModule.default.collectFinancialConnectionsAccounts(clientSecret,params),session=_yield$NativeStripeSd11.session,error=_yield$NativeStripeSd11.error;(_financialConnections11=financialConnectionsEventListener)==null||_financialConnections11.remove();if(error){return{error:error};}return{session:session};}catch(error){var _financialConnections12;(_financialConnections12=financialConnectionsEventListener)==null||_financialConnections12.remove();return{error:(0,_helpers.createError)(error)};}});return function collectFinancialConnectionsAccounts(_x25){return _ref27.apply(this,arguments);};}();var canAddCardToWallet=exports.canAddCardToWallet=function(){var _ref28=(0,_asyncToGenerator2.default)(function*(params){try{var _yield$NativeStripeSd12=yield _NativeStripeSdkModule.default.canAddCardToWallet(params),canAddCard=_yield$NativeStripeSd12.canAddCard,details=_yield$NativeStripeSd12.details,error=_yield$NativeStripeSd12.error;if(error){return{error:error};}return{canAddCard:canAddCard,details:details};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function canAddCardToWallet(_x26){return _ref28.apply(this,arguments);};}();var isCardInWallet=exports.isCardInWallet=function(){var _ref29=(0,_asyncToGenerator2.default)(function*(params){try{var _yield$NativeStripeSd13=yield _NativeStripeSdkModule.default.isCardInWallet(params),isInWallet=_yield$NativeStripeSd13.isInWallet,token=_yield$NativeStripeSd13.token,error=_yield$NativeStripeSd13.error;if(error){return{error:error};}return{isInWallet:isInWallet,token:token};}catch(error){return{error:(0,_helpers.createError)(error)};}});return function isCardInWallet(_x27){return _ref29.apply(this,arguments);};}();var Constants=exports.Constants=_NativeStripeSdkModule.default.getConstants();var isPlatformPaySupported=exports.isPlatformPaySupported=function(){var _ref30=(0,_asyncToGenerator2.default)(function*(params){return yield _NativeStripeSdkModule.default.isPlatformPaySupported(params!=null?params:{});});return function isPlatformPaySupported(_x28){return _ref30.apply(this,arguments);};}();var confirmPlatformPaySetupIntent=exports.confirmPlatformPaySetupIntent=function(){var _ref31=(0,_asyncToGenerator2.default)(function*(clientSecret,params){try{var _ref32=yield _NativeStripeSdkModule.default.confirmPlatformPay(clientSecret,params,false),error=_ref32.error,setupIntent=_ref32.setupIntent;if(error){return{error:error};}return{setupIntent:setupIntent};}catch(error){return{error:error};}});return function confirmPlatformPaySetupIntent(_x29,_x30){return _ref31.apply(this,arguments);};}();var confirmPlatformPayPayment=exports.confirmPlatformPayPayment=function(){var _ref33=(0,_asyncToGenerator2.default)(function*(clientSecret,params){try{var _ref34=yield _NativeStripeSdkModule.default.confirmPlatformPay(clientSecret,params,true),error=_ref34.error,paymentIntent=_ref34.paymentIntent;if(error){return{error:error};}return{paymentIntent:paymentIntent};}catch(error){return{error:error};}});return function confirmPlatformPayPayment(_x31,_x32){return _ref33.apply(this,arguments);};}();var dismissPlatformPay=exports.dismissPlatformPay=function(){var _ref35=(0,_asyncToGenerator2.default)(function*(){if(_reactNative.Platform.OS!=='ios'){return false;}try{var didDismiss=yield _NativeStripeSdkModule.default.dismissPlatformPay();return didDismiss;}catch(error){return false;}});return function dismissPlatformPay(){return _ref35.apply(this,arguments);};}();var createPlatformPayPaymentMethod=exports.createPlatformPayPaymentMethod=function(){var _ref36=(0,_asyncToGenerator2.default)(function*(params){try{var _ref37=yield _NativeStripeSdkModule.default.createPlatformPayPaymentMethod(params,false),error=_ref37.error,paymentMethod=_ref37.paymentMethod,shippingContact=_ref37.shippingContact;if(error){return{error:error};}return{paymentMethod:paymentMethod,shippingContact:shippingContact};}catch(error){return{error:error};}});return function createPlatformPayPaymentMethod(_x33){return _ref36.apply(this,arguments);};}();var createPlatformPayToken=exports.createPlatformPayToken=function(){var _ref38=(0,_asyncToGenerator2.default)(function*(params){try{var _ref39=yield _NativeStripeSdkModule.default.createPlatformPayPaymentMethod(params,true),error=_ref39.error,token=_ref39.token,shippingContact=_ref39.shippingContact;if(error){return{error:error};}return{token:token,shippingContact:shippingContact};}catch(error){return{error:error};}});return function createPlatformPayToken(_x34){return _ref38.apply(this,arguments);};}();var updatePlatformPaySheet=exports.updatePlatformPaySheet=function(){var _ref40=(0,_asyncToGenerator2.default)(function*(params){if(_reactNative.Platform.OS!=='ios'){return{};}try{yield _NativeStripeSdkModule.default.updatePlatformPaySheet(params.applePay.cartItems,params.applePay.shippingMethods,params.applePay.errors);return{};}catch(error){return{error:error};}});return function updatePlatformPaySheet(_x35){return _ref40.apply(this,arguments);};}();var openPlatformPaySetup=exports.openPlatformPaySetup=function(){var _ref41=(0,_asyncToGenerator2.default)(function*(){if(_reactNative.Platform.OS==='ios'){yield _NativeStripeSdkModule.default.openApplePaySetup();}});return function openPlatformPaySetup(){return _ref41.apply(this,arguments);};}();
//# sourceMappingURL=functions.js.map