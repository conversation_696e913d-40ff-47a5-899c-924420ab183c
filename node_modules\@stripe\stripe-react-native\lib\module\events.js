var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.addListener=addListener;var _reactNative=require("react-native");var _NativeStripeSdkModule=_interopRequireDefault(require("./specs/NativeStripeSdkModule"));var compatEventEmitter=_NativeStripeSdkModule.default.onConfirmHandlerCallback==null?_reactNative.Platform.OS==='ios'?new _reactNative.NativeEventEmitter(_NativeStripeSdkModule.default):_reactNative.DeviceEventEmitter:null;function addListener(event,handler){if(compatEventEmitter!=null){return compatEventEmitter.addListener(event,handler);}return _NativeStripeSdkModule.default[event](handler);}
//# sourceMappingURL=events.js.map