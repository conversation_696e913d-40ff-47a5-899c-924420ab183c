{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_NativeStripeSdkModule", "_events", "fetchPaymentMethodsCallback", "attachPaymentMethodCallback", "detachPaymentMethodCallback", "setSelectedPaymentOptionCallback", "fetchSelectedPaymentOptionCallback", "setupIntentClientSecretForCustomerAttachCallback", "initialize", "_ref", "_asyncToGenerator2", "default", "params", "customerAdapterOverrides", "customerAdapter", "configureCustomerAdapterEventListeners", "_yield$NativeStripeSd", "NativeStripeSdk", "initCustomerSheet", "error", "_x", "apply", "arguments", "fetchPaymentMethods", "_fetchPaymentMethodsC", "remove", "addListener", "paymentMethods", "customerAdapterFetchPaymentMethodsCallback", "Error", "attachPaymentMethod", "_attachPaymentMethodC", "_ref4", "_ref3", "paymentMethodId", "paymentMethod", "customerAdapterAttachPaymentMethodCallback", "_x2", "detachPaymentMethod", "_detachPaymentMethodC", "_ref6", "_ref5", "customerAdapterDetachPaymentMethodCallback", "_x3", "setSelectedPaymentOption", "_setSelectedPaymentOp", "_ref8", "_ref7", "paymentOption", "customerAdapterSetSelectedPaymentOptionCallback", "_x4", "fetchSelectedPaymentOption", "_fetchSelectedPayment", "customerAdapterFetchSelectedPaymentOptionCallback", "setupIntentClientSecretForCustomerAttach", "_setupIntentClientSec", "clientSecret", "customerAdapterSetupIntentClientSecretForCustomerAttachCallback", "present", "_ref11", "length", "undefined", "presentCustomerSheet", "retrievePaymentOptionSelection", "_ref12", "retrieveCustomerSheetPaymentOptionSelection", "Component", "_ref13", "visible", "presentationStyle", "animationStyle", "style", "appearance", "setupIntentClientSecret", "customerId", "customerEphemeralKeySecret", "merchantDisplayName", "headerTextForSelectionScreen", "defaultBillingDetails", "billingDetailsCollectionConfiguration", "returnURL", "removeSavedPaymentMethodMessage", "applePayEnabled", "googlePayEnabled", "timeout", "onResult", "React", "useEffect", "then", "initResult", "presentResult", "CustomerSheet", "exports"], "sourceRoot": "../../../src", "sources": ["components/CustomerSheet.tsx"], "mappings": "2QAAA,IAAAA,MAAA,CAAAC,sBAAA,CAAAC,OAAA,WAEA,IAAAC,sBAAA,CAAAF,sBAAA,CAAAC,OAAA,oCASA,IAAAE,OAAA,CAAAF,OAAA,cAEA,GAAI,CAAAG,2BAAqD,CAAG,IAAI,CAChE,GAAI,CAAAC,2BAAqD,CAAG,IAAI,CAChE,GAAI,CAAAC,2BAAqD,CAAG,IAAI,CAChE,GAAI,CAAAC,gCAA0D,CAAG,IAAI,CACrE,GAAI,CAAAC,kCAA4D,CAAG,IAAI,CACvE,GAAI,CAAAC,gDAA0E,CAC5E,IAAI,CAGN,GAAM,CAAAC,UAAU,gBAAAC,IAAA,IAAAC,kBAAA,CAAAC,OAAA,EAAG,UACjBC,MAA+B,CAG3B,CACJ,GAAI,CAAAC,wBAAwB,CAAG,CAAC,CAAC,CACjC,GAAID,MAAM,CAACE,eAAe,CAAE,CAC1BD,wBAAwB,CAAGE,sCAAsC,CAC/DH,MAAM,CAACE,eACT,CAAC,CACH,CAEA,GAAI,CACF,IAAAE,qBAAA,MAAwB,CAAAC,8BAAe,CAACC,iBAAiB,CACvDN,MAAM,CACNC,wBACF,CAAC,CAHOM,KAAK,CAAAH,qBAAA,CAALG,KAAK,CAIb,GAAIA,KAAK,CAAE,CACT,MAAO,CAAEA,KAAK,CAALA,KAAM,CAAC,CAClB,CACA,MAAO,CAAC,CAAC,CACX,CAAE,MAAOA,KAAU,CAAE,CACnB,MAAO,CACLA,KAAK,CAALA,KACF,CAAC,CACH,CACF,CAAC,iBA1BK,CAAAX,UAAUA,CAAAY,EAAA,SAAAX,IAAA,CAAAY,KAAA,MAAAC,SAAA,OA0Bf,CAED,GAAM,CAAAP,sCAAsC,CAAG,QAAzC,CAAAA,sCAAsCA,CAC1CD,eAAgC,CACqB,CACrD,GAAIA,eAAe,CAACS,mBAAmB,CAAE,KAAAC,qBAAA,CACvC,CAAAA,qBAAA,CAAAtB,2BAA2B,SAA3BsB,qBAAA,CAA6BC,MAAM,CAAC,CAAC,CACrCvB,2BAA2B,CAAG,GAAAwB,mBAAW,EACvC,8CAA8C,IAAAhB,kBAAA,CAAAC,OAAA,EAC9C,WAAY,CACV,GAAIG,eAAe,CAACS,mBAAmB,CAAE,CACvC,GAAM,CAAAI,cAAc,MAAS,CAAAb,eAAe,CAACS,mBAAmB,CAAC,CAAC,CAClE,KAAM,CAAAN,8BAAe,CAACW,0CAA0C,CAC9DD,cACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAAE,KAAK,CACb,8HACF,CAAC,CACH,CACF,CAAC,CACH,CAAC,CACH,CAEA,GAAIf,eAAe,CAACgB,mBAAmB,CAAE,KAAAC,qBAAA,CACvC,CAAAA,qBAAA,CAAA5B,2BAA2B,SAA3B4B,qBAAA,CAA6BN,MAAM,CAAC,CAAC,CACrCtB,2BAA2B,CAAG,GAAAuB,mBAAW,EACvC,8CAA8C,gBAAAM,KAAA,IAAAtB,kBAAA,CAAAC,OAAA,EAC9C,UAAAsB,KAAA,CAA+B,IAAtB,CAAAC,eAAe,CAAAD,KAAA,CAAfC,eAAe,CACtB,GAAIpB,eAAe,CAACgB,mBAAmB,CAAE,CACvC,GAAM,CAAAK,aAAa,MACX,CAAArB,eAAe,CAACgB,mBAAmB,CAACI,eAAe,CAAC,CAC5D,KAAM,CAAAjB,8BAAe,CAACmB,0CAA0C,CAC9DD,aACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAAN,KAAK,CACb,8HACF,CAAC,CACH,CACF,CAAC,kBAAAQ,GAAA,SAAAL,KAAA,CAAAX,KAAA,MAAAC,SAAA,OACH,CAAC,CACH,CAEA,GAAIR,eAAe,CAACwB,mBAAmB,CAAE,KAAAC,qBAAA,CACvC,CAAAA,qBAAA,CAAAnC,2BAA2B,SAA3BmC,qBAAA,CAA6Bd,MAAM,CAAC,CAAC,CACrCrB,2BAA2B,CAAG,GAAAsB,mBAAW,EACvC,8CAA8C,gBAAAc,KAAA,IAAA9B,kBAAA,CAAAC,OAAA,EAC9C,UAAA8B,KAAA,CAA+B,IAAtB,CAAAP,eAAe,CAAAO,KAAA,CAAfP,eAAe,CACtB,GAAIpB,eAAe,CAACwB,mBAAmB,CAAE,CACvC,GAAM,CAAAH,aAAa,MACX,CAAArB,eAAe,CAACwB,mBAAmB,CAACJ,eAAe,CAAC,CAC5D,KAAM,CAAAjB,8BAAe,CAACyB,0CAA0C,CAC9DP,aACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAAN,KAAK,CACb,8HACF,CAAC,CACH,CACF,CAAC,kBAAAc,GAAA,SAAAH,KAAA,CAAAnB,KAAA,MAAAC,SAAA,OACH,CAAC,CACH,CAEA,GAAIR,eAAe,CAAC8B,wBAAwB,CAAE,KAAAC,qBAAA,CAC5C,CAAAA,qBAAA,CAAAxC,gCAAgC,SAAhCwC,qBAAA,CAAkCpB,MAAM,CAAC,CAAC,CAC1CpB,gCAAgC,CAAG,GAAAqB,mBAAW,EAC5C,mDAAmD,gBAAAoB,KAAA,IAAApC,kBAAA,CAAAC,OAAA,EACnD,UAAAoC,KAAA,CAA6B,IAApB,CAAAC,aAAa,CAAAD,KAAA,CAAbC,aAAa,CACpB,GAAIlC,eAAe,CAAC8B,wBAAwB,CAAE,CAC5C,KAAM,CAAA9B,eAAe,CAAC8B,wBAAwB,CAACI,aAAa,CAAC,CAC7D,KAAM,CAAA/B,8BAAe,CAACgC,+CAA+C,CAAC,CAAC,CACzE,CAAC,IAAM,CACL,KAAM,IAAI,CAAApB,KAAK,CACb,mIACF,CAAC,CACH,CACF,CAAC,kBAAAqB,GAAA,SAAAJ,KAAA,CAAAzB,KAAA,MAAAC,SAAA,OACH,CAAC,CACH,CAEA,GAAIR,eAAe,CAACqC,0BAA0B,CAAE,KAAAC,qBAAA,CAC9C,CAAAA,qBAAA,CAAA9C,kCAAkC,SAAlC8C,qBAAA,CAAoC3B,MAAM,CAAC,CAAC,CAC5CnB,kCAAkC,CAAG,GAAAoB,mBAAW,EAC9C,qDAAqD,IAAAhB,kBAAA,CAAAC,OAAA,EACrD,WAAY,CACV,GAAIG,eAAe,CAACqC,0BAA0B,CAAE,CAC9C,GAAM,CAAAH,aAAa,MACX,CAAAlC,eAAe,CAACqC,0BAA0B,CAAC,CAAC,CACpD,KAAM,CAAAlC,8BAAe,CAACoC,iDAAiD,CACrEL,aACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAAnB,KAAK,CACb,qIACF,CAAC,CACH,CACF,CAAC,CACH,CAAC,CACH,CAEA,GAAIf,eAAe,CAACwC,wCAAwC,CAAE,KAAAC,qBAAA,CAC5D,CAAAA,qBAAA,CAAAhD,gDAAgD,SAAhDgD,qBAAA,CAAkD9B,MAAM,CAAC,CAAC,CAC1DlB,gDAAgD,CAAG,GAAAmB,mBAAW,EAC5D,mEAAmE,IAAAhB,kBAAA,CAAAC,OAAA,EACnE,WAAY,CACV,GAAIG,eAAe,CAACwC,wCAAwC,CAAE,CAC5D,GAAM,CAAAE,YAAY,MACV,CAAA1C,eAAe,CAACwC,wCAAwC,CAAC,CAAC,CAClE,KAAM,CAAArC,8BAAe,CAACwC,+DAA+D,CACnFD,YACF,CAAC,CACH,CAAC,IAAM,CACL,KAAM,IAAI,CAAA3B,KAAK,CACb,mJACF,CAAC,CACH,CACF,CAAC,CACH,CAAC,CACH,CAEA,MAAO,CACLN,mBAAmB,CAAE,CAAC,CAACT,eAAe,CAACS,mBAAmB,CAC1DO,mBAAmB,CAAE,CAAC,CAAChB,eAAe,CAACgB,mBAAmB,CAC1DQ,mBAAmB,CAAE,CAAC,CAACxB,eAAe,CAACwB,mBAAmB,CAC1DM,wBAAwB,CAAE,CAAC,CAAC9B,eAAe,CAAC8B,wBAAwB,CACpEO,0BAA0B,CAAE,CAAC,CAACrC,eAAe,CAACqC,0BAA0B,CACxEG,wCAAwC,CACtC,CAAC,CAACxC,eAAe,CAACwC,wCACtB,CAAC,CACH,CAAC,CAGD,GAAM,CAAAI,OAAO,gBAAAC,MAAA,IAAAjD,kBAAA,CAAAC,OAAA,EAAG,WAEmB,IADjC,CAAAC,MAAkC,CAAAU,SAAA,CAAAsC,MAAA,IAAAtC,SAAA,MAAAuC,SAAA,CAAAvC,SAAA,IAAG,CAAC,CAAC,CAEvC,GAAI,CACF,YAAa,CAAAL,8BAAe,CAAC6C,oBAAoB,CAAClD,MAAM,CAAC,CAC3D,CAAE,MAAOO,KAAU,CAAE,CACnB,MAAO,CACLA,KAAK,CAALA,KACF,CAAC,CACH,CACF,CAAC,iBAVK,CAAAuC,OAAOA,CAAA,SAAAC,MAAA,CAAAtC,KAAA,MAAAC,SAAA,OAUZ,CAMD,GAAM,CAAAyC,8BAA8B,gBAAAC,MAAA,IAAAtD,kBAAA,CAAAC,OAAA,EAClC,WAA0C,CACxC,GAAI,CACF,YAAa,CAAAM,8BAAe,CAACgD,2CAA2C,CAAC,CAAC,CAC5E,CAAE,MAAO9C,KAAU,CAAE,CACnB,MAAO,CACLA,KAAK,CAALA,KACF,CAAC,CACH,CACF,CAAC,iBATG,CAAA4C,8BAA8BA,CAAA,SAAAC,MAAA,CAAA3C,KAAA,MAAAC,SAAA,OASjC,CAiDH,QAAS,CAAA4C,SAASA,CAAAC,MAAA,CAoBR,IAnBR,CAAAC,OAAO,CAAAD,MAAA,CAAPC,OAAO,CACPC,iBAAiB,CAAAF,MAAA,CAAjBE,iBAAiB,CACjBC,cAAc,CAAAH,MAAA,CAAdG,cAAc,CACdC,KAAK,CAAAJ,MAAA,CAALI,KAAK,CACLC,UAAU,CAAAL,MAAA,CAAVK,UAAU,CACVC,uBAAuB,CAAAN,MAAA,CAAvBM,uBAAuB,CACvBC,UAAU,CAAAP,MAAA,CAAVO,UAAU,CACVC,0BAA0B,CAAAR,MAAA,CAA1BQ,0BAA0B,CAC1BC,mBAAmB,CAAAT,MAAA,CAAnBS,mBAAmB,CACnBC,4BAA4B,CAAAV,MAAA,CAA5BU,4BAA4B,CAC5BC,qBAAqB,CAAAX,MAAA,CAArBW,qBAAqB,CACrBC,qCAAqC,CAAAZ,MAAA,CAArCY,qCAAqC,CACrCC,SAAS,CAAAb,MAAA,CAATa,SAAS,CACTC,+BAA+B,CAAAd,MAAA,CAA/Bc,+BAA+B,CAC/BC,eAAe,CAAAf,MAAA,CAAfe,eAAe,CACfC,gBAAgB,CAAAhB,MAAA,CAAhBgB,gBAAgB,CAChBC,OAAO,CAAAjB,MAAA,CAAPiB,OAAO,CACPC,QAAQ,CAAAlB,MAAA,CAARkB,QAAQ,CACRvE,eAAe,CAAAqD,MAAA,CAAfrD,eAAe,CAEfwE,cAAK,CAACC,SAAS,CAAC,UAAM,CACpB,GAAInB,OAAO,CAAE,CACX5D,UAAU,CAAC,CACT+D,KAAK,CAALA,KAAK,CACLC,UAAU,CAAVA,UAAU,CACVC,uBAAuB,CAAvBA,uBAAuB,CACvBC,UAAU,CAAVA,UAAU,CACVC,0BAA0B,CAA1BA,0BAA0B,CAC1BC,mBAAmB,CAAnBA,mBAAmB,CACnBC,4BAA4B,CAA5BA,4BAA4B,CAC5BC,qBAAqB,CAArBA,qBAAqB,CACrBC,qCAAqC,CAArCA,qCAAqC,CACrCC,SAAS,CAATA,SAAS,CACTC,+BAA+B,CAA/BA,+BAA+B,CAC/BC,eAAe,CAAfA,eAAe,CACfC,gBAAgB,CAAhBA,gBAAgB,CAChBrE,eAAe,CAAfA,eACF,CAAC,CAAC,CAAC0E,IAAI,CAAC,SAACC,UAAU,CAAK,CACtB,GAAIA,UAAU,CAACtE,KAAK,CAAE,CACpBkE,QAAQ,CAACI,UAAU,CAAC,CACtB,CAAC,IAAM,CACL/B,OAAO,CAAC,CACN0B,OAAO,CAAPA,OAAO,CACPf,iBAAiB,CAAjBA,iBAAiB,CACjBC,cAAc,CAAdA,cACF,CAAC,CAAC,CAACkB,IAAI,CAAC,SAACE,aAAa,CAAK,CACzBL,QAAQ,CAACK,aAAa,CAAC,CACzB,CAAC,CAAC,CACJ,CACF,CAAC,CAAC,CACJ,CAGF,CAAC,CAAE,CAACtB,OAAO,CAAC,CAAC,CAEb,MAAO,KAAI,CACb,CAKO,GAAM,CAAAuB,aAAa,CAAAC,OAAA,CAAAD,aAAA,CAAG,CAC3BzB,SAAS,CAATA,SAAS,CACT1D,UAAU,CAAVA,UAAU,CACVkD,OAAO,CAAPA,OAAO,CACPK,8BAA8B,CAA9BA,8BACF,CAAC", "ignoreList": []}