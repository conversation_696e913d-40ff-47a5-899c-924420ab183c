{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "_NativeStripeSdkModule", "_PlatformPay", "_NativeApplePayButton", "_NativeGooglePayButton", "_jsxRuntime", "_jsxFileName", "_excluded", "PlatformPayButton", "_ref", "_ref$type", "type", "ButtonType", "<PERSON><PERSON><PERSON>", "_ref$appearance", "appearance", "ButtonStyle", "Automatic", "onPress", "disabled", "borderRadius", "onShippingMethodSelected", "onShippingContactSelected", "onCouponCodeEntered", "setOrderTracking", "style", "props", "_objectWithoutProperties2", "default", "shippingMethodCallback", "value", "nativeEvent", "undefined", "shippingContactCallback", "couponCodeCallback", "orderTrackingCallback", "NativeStripeSdk", "configureOrderTracking", "jsx", "TouchableOpacity", "activeOpacity", "styles", "notDisabled", "children", "Platform", "OS", "Object", "assign", "buttonStyle", "onShippingMethodSelectedAction", "onShippingContactSelectedAction", "onCouponCodeEnteredAction", "onOrderTrackingAction", "nativeButtonStyle", "StyleSheet", "create", "flex", "opacity"], "sourceRoot": "../../../src", "sources": ["components/PlatformPayButton.tsx"], "mappings": "wSAAA,IAAAA,MAAA,CAAAC,sBAAA,CAAAC,OAAA,WACA,IAAAC,YAAA,CAAAD,OAAA,iBASA,IAAAE,sBAAA,CAAAH,sBAAA,CAAAC,OAAA,oCACA,IAAAG,YAAA,CAAAH,OAAA,yBAMA,IAAAI,qBAAA,CAAAL,sBAAA,CAAAC,OAAA,mCACA,IAAAK,sBAAA,CAAAN,sBAAA,CAAAC,OAAA,oCAAmE,IAAAM,WAAA,CAAAN,OAAA,0BAAAO,YAAA,qFAAAC,SAAA,mKA4E5D,QAAS,CAAAC,iBAAiBA,CAAAC,IAAA,CAYvB,KAAAC,SAAA,CAAAD,IAAA,CAXRE,IAAI,CAAJA,IAAI,CAAAD,SAAA,UAAGE,uBAAU,CAACC,OAAO,CAAAH,SAAA,CAAAI,eAAA,CAAAL,IAAA,CACzBM,UAAU,CAAVA,UAAU,CAAAD,eAAA,UAAGE,wBAAW,CAACC,SAAS,CAAAH,eAAA,CAClCI,OAAO,CAAAT,IAAA,CAAPS,OAAO,CACPC,QAAQ,CAAAV,IAAA,CAARU,QAAQ,CACRC,YAAY,CAAAX,IAAA,CAAZW,YAAY,CACZC,wBAAwB,CAAAZ,IAAA,CAAxBY,wBAAwB,CACxBC,yBAAyB,CAAAb,IAAA,CAAzBa,yBAAyB,CACzBC,mBAAmB,CAAAd,IAAA,CAAnBc,mBAAmB,CACnBC,gBAAgB,CAAAf,IAAA,CAAhBe,gBAAgB,CAChBC,KAAK,CAAAhB,IAAA,CAALgB,KAAK,CACFC,KAAK,IAAAC,yBAAA,CAAAC,OAAA,EAAAnB,IAAA,CAAAF,SAAA,EAER,GAAM,CAAAsB,sBAAsB,CAAGR,wBAAwB,CACnD,SACES,KAEE,CACC,CACHT,wBAAwB,EAAIA,wBAAwB,CAACS,KAAK,CAACC,WAAW,CAAC,CACzE,CAAC,CACDC,SAAS,CAEb,GAAM,CAAAC,uBAAuB,CAAGX,yBAAyB,CACrD,SACEQ,KAEE,CACC,CACHR,yBAAyB,CAACQ,KAAK,CAACC,WAAW,CAAC,CAC9C,CAAC,CACDC,SAAS,CAEb,GAAM,CAAAE,kBAAkB,CAAGX,mBAAmB,CAC1C,SACEO,KAEE,CACC,CACHP,mBAAmB,EAAIA,mBAAmB,CAACO,KAAK,CAACC,WAAW,CAAC,CAC/D,CAAC,CACDC,SAAS,CAEb,GAAM,CAAAG,qBAAqB,CAAGX,gBAAgB,CAC1C,UAAM,CACJA,gBAAgB,CAACY,8BAAe,CAACC,sBAAsB,CAAC,CAC1D,CAAC,CACDL,SAAS,CAEb,MACE,GAAA3B,WAAA,CAAAiC,GAAA,EAACtC,YAAA,CAAAuC,gBAAgB,EACfpB,QAAQ,CAAEA,QAAS,CACnBqB,aAAa,CAAErB,QAAQ,CAAG,GAAG,CAAG,CAAE,CAClCD,OAAO,CAAEA,OAAQ,CACjBO,KAAK,CAAE,CAACN,QAAQ,CAAGsB,MAAM,CAACtB,QAAQ,CAAGsB,MAAM,CAACC,WAAW,CAAEjB,KAAK,CAAE,CAAAkB,QAAA,CAE/DC,qBAAQ,CAACC,EAAE,GAAK,KAAK,CACpB,GAAAxC,WAAA,CAAAiC,GAAA,EAACnC,qBAAA,CAAAyB,OAAoB,CAAAkB,MAAA,CAAAC,MAAA,EACnBpC,IAAI,CAAEA,IAAK,CACXqC,WAAW,CAAEjC,UAAW,CACxBK,YAAY,CAAEA,YAAa,CAC3BD,QAAQ,CAAEA,QAAQ,OAARA,QAAQ,CAAI,KAAM,CAC5B8B,8BAA8B,CAAEpB,sBAAuB,CACvDqB,+BAA+B,CAAEjB,uBAAwB,CACzDkB,yBAAyB,CAAEjB,kBAAmB,CAC9CkB,qBAAqB,CAAEjB,qBAAsB,CAC7CV,KAAK,CAAEgB,MAAM,CAACY,iBAAkB,EAC5B3B,KAAK,CACV,CAAC,CAEF,GAAArB,WAAA,CAAAiC,GAAA,EAAClC,sBAAA,CAAAwB,OAAqB,CAAAkB,MAAA,CAAAC,MAAA,EACpBpC,IAAI,CAAEA,IAAK,CACXI,UAAU,CAAEA,UAAW,CACvBK,YAAY,CAAEA,YAAa,CAC3BK,KAAK,CAAEgB,MAAM,CAACY,iBAAkB,EAC5B3B,KAAK,CACV,CACF,CACe,CAAC,CAEvB,CAEA,GAAM,CAAAe,MAAM,CAAGa,uBAAU,CAACC,MAAM,CAAC,CAC/BpC,QAAQ,CAAE,CACRqC,IAAI,CAAE,CAAC,CACPC,OAAO,CAAE,GACX,CAAC,CACDf,WAAW,CAAE,CACXc,IAAI,CAAE,CACR,CAAC,CACDH,iBAAiB,CAAE,CAAEG,IAAI,CAAE,CAAE,CAC/B,CAAC,CAAC", "ignoreList": []}