var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.CardField=void 0;var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireWildcard(require("react"));var _helpers=require("../helpers");var _NativeCardField=_interopRequireWildcard(require("../specs/NativeCardField"));var _jsxRuntime=require("react/jsx-runtime");var _excluded=["onCardChange","onFocus","onBlur","cardStyle","placeholders","autofocus","postalCodeEnabled","disabled","dangerouslyGetFullCardDetails"];var _this=this,_jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/CardField.tsx";function _getRequireWildcardCache(e){if("function"!=typeof WeakMap)return null;var r=new WeakMap(),t=new WeakMap();return(_getRequireWildcardCache=function _getRequireWildcardCache(e){return e?t:r;})(e);}function _interopRequireWildcard(e,r){if(!r&&e&&e.__esModule)return e;if(null===e||"object"!=typeof e&&"function"!=typeof e)return{default:e};var t=_getRequireWildcardCache(r);if(t&&t.has(e))return t.get(e);var n={__proto__:null},a=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var u in e)if("default"!==u&&{}.hasOwnProperty.call(e,u)){var i=a?Object.getOwnPropertyDescriptor(e,u):null;i&&(i.get||i.set)?Object.defineProperty(n,u,i):n[u]=e[u];}return n.default=e,t&&t.set(e,n),n;}var CardField=exports.CardField=(0,_react.forwardRef)(function(_ref,ref){var onCardChange=_ref.onCardChange,onFocus=_ref.onFocus,onBlur=_ref.onBlur,cardStyle=_ref.cardStyle,placeholders=_ref.placeholders,autofocus=_ref.autofocus,postalCodeEnabled=_ref.postalCodeEnabled,disabled=_ref.disabled,dangerouslyGetFullCardDetails=_ref.dangerouslyGetFullCardDetails,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);var inputRef=(0,_react.useRef)(null);var onCardChangeHandler=(0,_react.useCallback)(function(event){var card=event.nativeEvent.card;var data={last4:card.last4,expiryMonth:card.expiryMonth,expiryYear:card.expiryYear,complete:card.complete,brand:card.brand,validExpiryDate:card.validExpiryDate,validNumber:card.validNumber,validCVC:card.validCVC};if(card.hasOwnProperty('postalCode')){data.postalCode=card.postalCode||'';}if(card.hasOwnProperty('number')||card.hasOwnProperty('cvc')){data.number=card.number||'';data.cvc=card.cvc||'';if(__DEV__&&onCardChange&&card.complete){console.warn(`[stripe-react-native] ⚠️ WARNING: You've enabled \`dangerouslyGetFullCardDetails\`, meaning full card details are being returned. Only do this if you're certain that you fulfill the necessary PCI compliance requirements. Make sure that you're not mistakenly logging or storing full card details! See the docs for details: https://stripe.com/docs/security/guide#validating-pci-compliance`);}}onCardChange==null||onCardChange(data);},[onCardChange]);var onFocusHandler=(0,_react.useCallback)(function(event){var focusedField=event.nativeEvent.focusedField;if(focusedField){(0,_helpers.focusInput)(inputRef.current);onFocus==null||onFocus(focusedField);}else{onBlur==null||onBlur();}},[onFocus,onBlur]);var focus=function focus(){_NativeCardField.Commands.focus(inputRef.current);};var blur=function blur(){_NativeCardField.Commands.blur(inputRef.current);};var clear=function clear(){_NativeCardField.Commands.clear(inputRef.current);};(0,_react.useImperativeHandle)(ref,function(){return{focus:focus,blur:blur,clear:clear};});(0,_react.useLayoutEffect)(function(){var inputRefValue=inputRef.current;if(inputRefValue!==null){(0,_helpers.registerInput)(inputRefValue);return function(){(0,_helpers.unregisterInput)(inputRefValue);if((0,_helpers.currentlyFocusedInput)()===inputRefValue){inputRefValue.blur();}};}return function(){};},[inputRef]);return(0,_jsxRuntime.jsx)(_NativeCardField.default,Object.assign({ref:inputRef,onCardChange:onCardChangeHandler,onFocusChange:onFocusHandler,cardStyle:{backgroundColor:cardStyle==null?void 0:cardStyle.backgroundColor,borderColor:cardStyle==null?void 0:cardStyle.borderColor,borderWidth:cardStyle==null?void 0:cardStyle.borderWidth,borderRadius:cardStyle==null?void 0:cardStyle.borderRadius,cursorColor:cardStyle==null?void 0:cardStyle.cursorColor,fontSize:cardStyle==null?void 0:cardStyle.fontSize,placeholderColor:cardStyle==null?void 0:cardStyle.placeholderColor,textColor:cardStyle==null?void 0:cardStyle.textColor,textErrorColor:cardStyle==null?void 0:cardStyle.textErrorColor,fontFamily:cardStyle==null?void 0:cardStyle.fontFamily},placeholders:{number:placeholders==null?void 0:placeholders.number,expiration:placeholders==null?void 0:placeholders.expiration,cvc:placeholders==null?void 0:placeholders.cvc,postalCode:placeholders==null?void 0:placeholders.postalCode},autofocus:autofocus!=null?autofocus:false,postalCodeEnabled:postalCodeEnabled!=null?postalCodeEnabled:true,disabled:disabled!=null?disabled:false,dangerouslyGetFullCardDetails:dangerouslyGetFullCardDetails!=null?dangerouslyGetFullCardDetails:false},props));});
//# sourceMappingURL=CardField.js.map