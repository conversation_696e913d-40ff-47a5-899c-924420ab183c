var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.AddToWalletButton=AddToWalletButton;var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireDefault(require("react"));var _NativeAddToWalletButton=_interopRequireDefault(require("../specs/NativeAddToWalletButton"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/AddToWalletButton.tsx";var _excluded=["onComplete"];function AddToWalletButton(_ref){var onComplete=_ref.onComplete,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);return(0,_jsxRuntime.jsx)(_NativeAddToWalletButton.default,Object.assign({},props,{onCompleteAction:function onCompleteAction(value){return onComplete(value.nativeEvent);}}));}
//# sourceMappingURL=AddToWalletButton.js.map