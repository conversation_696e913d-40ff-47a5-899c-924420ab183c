Object.defineProperty(exports,"__esModule",{value:true});exports.GooglePayCardTokenStatus=exports.CanAddCardToWalletStatus=void 0;var GooglePayCardTokenStatus=exports.GooglePayCardTokenStatus=function(GooglePayCardTokenStatus){GooglePayCardTokenStatus["TOKEN_STATE_NEEDS_IDENTITY_VERIFICATION"]="TOKEN_STATE_NEEDS_IDENTITY_VERIFICATION";GooglePayCardTokenStatus["TOKEN_STATE_PENDING"]="TOKEN_STATE_PENDING";GooglePayCardTokenStatus["TOKEN_STATE_SUSPENDED"]="TOKEN_STATE_SUSPENDED";GooglePayCardTokenStatus["TOKEN_STATE_ACTIVE"]="TOKEN_STATE_ACTIVE";GooglePayCardTokenStatus["TOKEN_STATE_FELICA_PENDING_PROVISIONING"]="TOKEN_STATE_FELICA_PENDING_PROVISIONING";GooglePayCardTokenStatus["TOKEN_STATE_UNTOKENIZED"]="TOKEN_STATE_UNTOKENIZED";return GooglePayCardTokenStatus;}({});var CanAddCardToWalletStatus=exports.CanAddCardToWalletStatus=function(CanAddCardToWalletStatus){CanAddCardToWalletStatus["MISSING_CONFIGURATION"]="MISSING_CONFIGURATION";CanAddCardToWalletStatus["UNSUPPORTED_DEVICE"]="UNSUPPORTED_DEVICE";CanAddCardToWalletStatus["CARD_ALREADY_EXISTS"]="CARD_ALREADY_EXISTS";CanAddCardToWalletStatus["CARD_EXISTS_ON_CURRENT_DEVICE"]="CARD_EXISTS_ON_CURRENT_DEVICE";CanAddCardToWalletStatus["CARD_EXISTS_ON_PAIRED_DEVICE"]="CARD_EXISTS_ON_PAIRED_DEVICE";return CanAddCardToWalletStatus;}({});
//# sourceMappingURL=PushProvisioning.js.map