/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.bridge.DynamicFromObject;
import com.facebook.react.bridge.ReadableMap;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class AddToWalletButtonManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & AddToWalletButtonManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public AddToWalletButtonManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "iOSButtonStyle":
        mViewManager.setIOSButtonStyle(view, value == null ? "onDarkBackground" : (String) value);
        break;
      case "androidAssetSource":
        mViewManager.setAndroidAssetSource(view, (ReadableMap) value);
        break;
      case "testEnv":
        mViewManager.setTestEnv(view, value == null ? false : (boolean) value);
        break;
      case "cardDetails":
        mViewManager.setCardDetails(view, new DynamicFromObject(value));
        break;
      case "token":
        mViewManager.setToken(view, new DynamicFromObject(value));
        break;
      case "ephemeralKey":
        mViewManager.setEphemeralKey(view, new DynamicFromObject(value));
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
