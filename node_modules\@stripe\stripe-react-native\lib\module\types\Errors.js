Object.defineProperty(exports,"__esModule",{value:true});exports.VerifyMicrodepositsError=exports.RetrieveSetupIntentError=exports.RetrievePaymentIntentError=exports.PlatformPayError=exports.PaymentSheetError=exports.MissingRoutingNumber=exports.GooglePayError=exports.CustomerSheetError=exports.CreateTokenError=exports.CreatePaymentMethodError=exports.ConfirmSetupIntentError=exports.ConfirmPaymentError=exports.CollectBankAccountError=exports.CardActionError=exports.ApplePayError=exports.AddressSheetError=void 0;var ConfirmPaymentError=exports.ConfirmPaymentError=function(ConfirmPaymentError){ConfirmPaymentError["Canceled"]="Canceled";ConfirmPaymentError["Failed"]="Failed";ConfirmPaymentError["Unknown"]="Unknown";return ConfirmPaymentError;}({});var CardActionError=exports.CardActionError=function(CardActionError){CardActionError["Canceled"]="Canceled";CardActionError["Failed"]="Failed";CardActionError["Unknown"]="Unknown";return CardActionError;}({});var ConfirmSetupIntentError=exports.ConfirmSetupIntentError=function(ConfirmSetupIntentError){ConfirmSetupIntentError["Canceled"]="Canceled";ConfirmSetupIntentError["Failed"]="Failed";ConfirmSetupIntentError["Unknown"]="Unknown";return ConfirmSetupIntentError;}({});var CreatePaymentMethodError=exports.CreatePaymentMethodError=function(CreatePaymentMethodError){CreatePaymentMethodError["Failed"]="Failed";return CreatePaymentMethodError;}({});var CreateTokenError=exports.CreateTokenError=function(CreateTokenError){CreateTokenError["Failed"]="Failed";return CreateTokenError;}({});var RetrievePaymentIntentError=exports.RetrievePaymentIntentError=function(RetrievePaymentIntentError){RetrievePaymentIntentError["Unknown"]="Unknown";return RetrievePaymentIntentError;}({});var RetrieveSetupIntentError=exports.RetrieveSetupIntentError=function(RetrieveSetupIntentError){RetrieveSetupIntentError["Unknown"]="Unknown";return RetrieveSetupIntentError;}({});var ApplePayError=exports.ApplePayError=function(ApplePayError){ApplePayError["Canceled"]="Canceled";ApplePayError["Failed"]="Failed";ApplePayError["Unknown"]="Unknown";return ApplePayError;}({});var PaymentSheetError=exports.PaymentSheetError=function(PaymentSheetError){PaymentSheetError["Failed"]="Failed";PaymentSheetError["Canceled"]="Canceled";PaymentSheetError["Timeout"]="Timeout";return PaymentSheetError;}({});var GooglePayError=exports.GooglePayError=function(GooglePayError){GooglePayError["Failed"]="Failed";GooglePayError["Canceled"]="Canceled";GooglePayError["Unknown"]="Unknown";return GooglePayError;}({});var MissingRoutingNumber=exports.MissingRoutingNumber={code:CreateTokenError.Failed,message:'You must provide a routing number for US bank accounts. This should be the ACH routing number.'};var VerifyMicrodepositsError=exports.VerifyMicrodepositsError=function(VerifyMicrodepositsError){VerifyMicrodepositsError["Canceled"]="Canceled";VerifyMicrodepositsError["Failed"]="Failed";VerifyMicrodepositsError["Unknown"]="Unknown";return VerifyMicrodepositsError;}({});var CollectBankAccountError=exports.CollectBankAccountError=function(CollectBankAccountError){CollectBankAccountError["Canceled"]="Canceled";CollectBankAccountError["Failed"]="Failed";CollectBankAccountError["Unknown"]="Unknown";return CollectBankAccountError;}({});var AddressSheetError=exports.AddressSheetError=function(AddressSheetError){AddressSheetError["Failed"]="Failed";AddressSheetError["Canceled"]="Canceled";return AddressSheetError;}({});var CustomerSheetError=exports.CustomerSheetError=function(CustomerSheetError){CustomerSheetError["Failed"]="Failed";CustomerSheetError["Canceled"]="Canceled";return CustomerSheetError;}({});var PlatformPayError=exports.PlatformPayError=function(PlatformPayError){PlatformPayError["Canceled"]="Canceled";PlatformPayError["Failed"]="Failed";PlatformPayError["Unknown"]="Unknown";return PlatformPayError;}({});
//# sourceMappingURL=Errors.js.map