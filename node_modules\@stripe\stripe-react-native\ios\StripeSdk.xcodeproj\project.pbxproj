// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 46;
	objects = {

/* Begin PBXBuildFile section */
		24172CD126284587005FB3C6 /* CardFieldDelegate.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24172CD026284587005FB3C6 /* CardFieldDelegate.swift */; };
		243A967D269C223B0037175A /* CardFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 243A967C269C223B0037175A /* CardFormView.swift */; };
		243A967F269C22530037175A /* CardFormViewManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 243A967E269C22530037175A /* CardFormViewManager.swift */; };
		243A9681269C226A0037175A /* CardFormViewManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 243A9680269C226A0037175A /* CardFormViewManager.m */; };
		247D86412601DC7400EBE2C2 /* PaymentMethodFactory.swift in Sources */ = {isa = PBXBuildFile; fileRef = 247D86402601DC7400EBE2C2 /* PaymentMethodFactory.swift */; };
		2490F52D256D08FB0027A42B /* ApplePayButtonManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2490F52C256D08FB0027A42B /* ApplePayButtonManager.swift */; };
		2490F52F256D0B5A0027A42B /* ApplePayButtonManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 2490F52E256D0B5A0027A42B /* ApplePayButtonManager.m */; };
		2490F531256D1EDA0027A42B /* ApplePayButtonView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 2490F530256D1EDA0027A42B /* ApplePayButtonView.swift */; };
		249BA096256CF67B00B32764 /* UIColorExtension.swift in Sources */ = {isa = PBXBuildFile; fileRef = 249BA095256CF67B00B32764 /* UIColorExtension.swift */; };
		24EA32E3262446DC007AF858 /* BECSDebitFormManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24EA32E2262446DC007AF858 /* BECSDebitFormManager.swift */; };
		24EA32E6262446E6007AF858 /* BECSDebitFormView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24EA32E5262446E6007AF858 /* BECSDebitFormView.swift */; };
		24EA331C26244898007AF858 /* BECSDebitFormManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 24EA331B26244898007AF858 /* BECSDebitFormManager.m */; };
		24FF2C37256530F9002FF847 /* CardFieldManager.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24FF2C36256530F9002FF847 /* CardFieldManager.swift */; };
		24FF2C3925653109002FF847 /* CardFieldManager.m in Sources */ = {isa = PBXBuildFile; fileRef = 24FF2C3825653109002FF847 /* CardFieldManager.m */; };
		24FF2C3B25653114002FF847 /* CardFieldView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 24FF2C3A25653114002FF847 /* CardFieldView.swift */; };
/* End PBXBuildFile section */

/* Begin PBXCopyFilesBuildPhase section */
		58B511D91A9E6C8500147676 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "include/$(PRODUCT_NAME)";
			dstSubfolderSpec = 16;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		134814201AA4EA6300B7C361 /* libStripeSdk.a */ = {isa = PBXFileReference; explicitFileType = archive.ar; includeInIndex = 0; path = libStripeSdk.a; sourceTree = BUILT_PRODUCTS_DIR; };
		24172CD026284587005FB3C6 /* CardFieldDelegate.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardFieldDelegate.swift; sourceTree = "<group>"; };
		243A967C269C223B0037175A /* CardFormView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardFormView.swift; sourceTree = "<group>"; };
		243A967E269C22530037175A /* CardFormViewManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardFormViewManager.swift; sourceTree = "<group>"; };
		243A9680269C226A0037175A /* CardFormViewManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CardFormViewManager.m; sourceTree = "<group>"; };
		247D86402601DC7400EBE2C2 /* PaymentMethodFactory.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = PaymentMethodFactory.swift; sourceTree = "<group>"; };
		2490F52C256D08FB0027A42B /* ApplePayButtonManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApplePayButtonManager.swift; sourceTree = "<group>"; };
		2490F52E256D0B5A0027A42B /* ApplePayButtonManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = ApplePayButtonManager.m; sourceTree = "<group>"; };
		2490F530256D1EDA0027A42B /* ApplePayButtonView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ApplePayButtonView.swift; sourceTree = "<group>"; };
		249BA095256CF67B00B32764 /* UIColorExtension.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = UIColorExtension.swift; sourceTree = "<group>"; };
		24EA32E2262446DC007AF858 /* BECSDebitFormManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BECSDebitFormManager.swift; sourceTree = "<group>"; };
		24EA32E5262446E6007AF858 /* BECSDebitFormView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = BECSDebitFormView.swift; sourceTree = "<group>"; };
		24EA331B26244898007AF858 /* BECSDebitFormManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = BECSDebitFormManager.m; sourceTree = "<group>"; };
		24FF2C36256530F9002FF847 /* CardFieldManager.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardFieldManager.swift; sourceTree = "<group>"; };
		24FF2C3825653109002FF847 /* CardFieldManager.m */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.objc; path = CardFieldManager.m; sourceTree = "<group>"; };
		24FF2C3A25653114002FF847 /* CardFieldView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CardFieldView.swift; sourceTree = "<group>"; };
		F4FF95D5245B92E700C19C63 /* StripeSdk-Bridging-Header.h */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.c.h; path = "StripeSdk-Bridging-Header.h"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		58B511D81A9E6C8500147676 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		134814211AA4EA7D00B7C361 /* Products */ = {
			isa = PBXGroup;
			children = (
				134814201AA4EA6300B7C361 /* libStripeSdk.a */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		58B511D21A9E6C8500147676 = {
			isa = PBXGroup;
			children = (
				243A9680269C226A0037175A /* CardFormViewManager.m */,
				243A967E269C22530037175A /* CardFormViewManager.swift */,
				243A967C269C223B0037175A /* CardFormView.swift */,
				24EA331B26244898007AF858 /* BECSDebitFormManager.m */,
				24EA32E5262446E6007AF858 /* BECSDebitFormView.swift */,
				24EA32E2262446DC007AF858 /* BECSDebitFormManager.swift */,
				24172CD026284587005FB3C6 /* CardFieldDelegate.swift */,
				247D86402601DC7400EBE2C2 /* PaymentMethodFactory.swift */,
				2490F530256D1EDA0027A42B /* ApplePayButtonView.swift */,
				2490F52E256D0B5A0027A42B /* ApplePayButtonManager.m */,
				2490F52C256D08FB0027A42B /* ApplePayButtonManager.swift */,
				249BA095256CF67B00B32764 /* UIColorExtension.swift */,
				F4FF95D5245B92E700C19C63 /* StripeSdk-Bridging-Header.h */,
				24FF2C36256530F9002FF847 /* CardFieldManager.swift */,
				24FF2C3825653109002FF847 /* CardFieldManager.m */,
				24FF2C3A25653114002FF847 /* CardFieldView.swift */,
				134814211AA4EA7D00B7C361 /* Products */,
			);
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		58B511DA1A9E6C8500147676 /* StripeSdk */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "StripeSdk" */;
			buildPhases = (
				58B511D71A9E6C8500147676 /* Sources */,
				58B511D81A9E6C8500147676 /* Frameworks */,
				58B511D91A9E6C8500147676 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = StripeSdk;
			productName = RCTDataManager;
			productReference = 134814201AA4EA6300B7C361 /* libStripeSdk.a */;
			productType = "com.apple.product-type.library.static";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		58B511D31A9E6C8500147676 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 0920;
				ORGANIZATIONNAME = Facebook;
				TargetAttributes = {
					58B511DA1A9E6C8500147676 = {
						CreatedOnToolsVersion = 6.1.1;
					};
				};
			};
			buildConfigurationList = 58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "StripeSdk" */;
			compatibilityVersion = "Xcode 3.2";
			developmentRegion = English;
			hasScannedForEncodings = 0;
			knownRegions = (
				English,
				en,
			);
			mainGroup = 58B511D21A9E6C8500147676;
			productRefGroup = 58B511D21A9E6C8500147676;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				58B511DA1A9E6C8500147676 /* StripeSdk */,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		58B511D71A9E6C8500147676 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				243A967D269C223B0037175A /* CardFormView.swift in Sources */,
				24172CD126284587005FB3C6 /* CardFieldDelegate.swift in Sources */,
				24FF2C37256530F9002FF847 /* CardFieldManager.swift in Sources */,
				24FF2C3925653109002FF847 /* CardFieldManager.m in Sources */,
				24FF2C3B25653114002FF847 /* CardFieldView.swift in Sources */,
				24EA331C26244898007AF858 /* BECSDebitFormManager.m in Sources */,
				243A967F269C22530037175A /* CardFormViewManager.swift in Sources */,
				2490F52F256D0B5A0027A42B /* ApplePayButtonManager.m in Sources */,
				247D86412601DC7400EBE2C2 /* PaymentMethodFactory.swift in Sources */,
				2490F52D256D08FB0027A42B /* ApplePayButtonManager.swift in Sources */,
				24EA32E6262446E6007AF858 /* BECSDebitFormView.swift in Sources */,
				249BA096256CF67B00B32764 /* UIColorExtension.swift in Sources */,
				2490F531256D1EDA0027A42B /* ApplePayButtonView.swift in Sources */,
				24EA32E3262446DC007AF858 /* BECSDebitFormManager.swift in Sources */,
				243A9681269C226A0037175A /* CardFormViewManager.m in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		58B511ED1A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_SYMBOLS_PRIVATE_EXTERN = NO;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
			};
			name = Debug;
		};
		58B511EE1A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_CXX_LIBRARY = "libc++";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = YES;
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu99;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 8.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				SDKROOT = iphoneos;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		58B511F01A9E6C8500147676 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = StripeSdk;
				SKIP_INSTALL = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "StripeSdk-Bridging-Header.h";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		58B511F11A9E6C8500147676 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					/Applications/Xcode.app/Contents/Developer/Toolchains/XcodeDefault.xctoolchain/usr/include,
					"$(SRCROOT)/../../../React/**",
					"$(SRCROOT)/../../react-native/React/**",
				);
				LIBRARY_SEARCH_PATHS = "$(inherited)";
				OTHER_LDFLAGS = "-ObjC";
				PRODUCT_NAME = StripeSdk;
				SKIP_INSTALL = YES;
				SWIFT_OBJC_BRIDGING_HEADER = "StripeSdk-Bridging-Header.h";
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		58B511D61A9E6C8500147676 /* Build configuration list for PBXProject "StripeSdk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511ED1A9E6C8500147676 /* Debug */,
				58B511EE1A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		58B511EF1A9E6C8500147676 /* Build configuration list for PBXNativeTarget "StripeSdk" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				58B511F01A9E6C8500147676 /* Debug */,
				58B511F11A9E6C8500147676 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 58B511D31A9E6C8500147676 /* Project object */;
}
