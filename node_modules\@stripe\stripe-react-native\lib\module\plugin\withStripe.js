Object.defineProperty(exports,"__esModule",{value:true});exports.default=void 0;exports.setApplePayEntitlement=setApplePayEntitlement;exports.setGooglePayMetaData=setGooglePayMetaData;exports.withNoopSwiftFile=void 0;var _configPlugins=require("@expo/config-plugins");var _AndroidConfig$Manife=_configPlugins.AndroidConfig.Manifest,addMetaDataItemToMainApplication=_AndroidConfig$Manife.addMetaDataItemToMainApplication,getMainApplicationOrThrow=_AndroidConfig$Manife.getMainApplicationOrThrow,removeMetaDataItemFromMainApplication=_AndroidConfig$Manife.removeMetaDataItemFromMainApplication;var pkg=require('@stripe/stripe-react-native/package.json');var withStripe=function withStripe(config,props){config=withStripeIos(config,props);config=withNoopSwiftFile(config);config=withStripeAndroid(config,props);return config;};var withStripeIos=function withStripeIos(expoConfig,_ref){var merchantIdentifier=_ref.merchantIdentifier;return(0,_configPlugins.withEntitlementsPlist)(expoConfig,function(config){config.modResults=setApplePayEntitlement(merchantIdentifier,config.modResults);return config;});};function setApplePayEntitlement(merchantIdentifiers,entitlements){var _entitlements$key;var key='com.apple.developer.in-app-payments';var merchants=(_entitlements$key=entitlements[key])!=null?_entitlements$key:[];if(!Array.isArray(merchantIdentifiers)){merchantIdentifiers=[merchantIdentifiers];}for(var id of merchantIdentifiers){if(id&&!merchants.includes(id)){merchants.push(id);}}if(merchants.length){entitlements[key]=merchants;}return entitlements;}var withNoopSwiftFile=exports.withNoopSwiftFile=function withNoopSwiftFile(config){return _configPlugins.IOSConfig.XcodeProjectFile.withBuildSourceFile(config,{filePath:'noop-file.swift',contents:['//','// @generated','// A blank Swift file must be created for native modules with Swift files to work correctly.','//',''].join('\n')});};var withStripeAndroid=function withStripeAndroid(expoConfig,_ref2){var _ref2$enableGooglePay=_ref2.enableGooglePay,enableGooglePay=_ref2$enableGooglePay===void 0?false:_ref2$enableGooglePay;return(0,_configPlugins.withAndroidManifest)(expoConfig,function(config){config.modResults=setGooglePayMetaData(enableGooglePay,config.modResults);return config;});};function setGooglePayMetaData(enabled,modResults){var GOOGLE_PAY_META_NAME='com.google.android.gms.wallet.api.enabled';var mainApplication=getMainApplicationOrThrow(modResults);if(enabled){addMetaDataItemToMainApplication(mainApplication,GOOGLE_PAY_META_NAME,'true');}else{removeMetaDataItemFromMainApplication(mainApplication,GOOGLE_PAY_META_NAME);}return modResults;}var _default=exports.default=(0,_configPlugins.createRunOncePlugin)(withStripe,pkg.name,pkg.version);
//# sourceMappingURL=withStripe.js.map