import type { PlatformPay, CanAddCardToWalletParams, CanAddCardToWalletResult } from '../types';
/**
 * usePlatformPay hook. Access all Apple and Google Pay functionality with this hook.
 */
export declare function usePlatformPay(): {
    /** Use this boolean to present a spinner or other similar loading screen. `true` if the SDK is currently processing, `false` if it is not. */
    loading: boolean;
    /**
     * Check if the relevant native wallet (Apple Pay on iOS, Google Pay on Android) is supported.
     * @returns A boolean indicating whether or not the native wallet is supported.
     */
    isPlatformPaySupported: (params?: {
        googlePay?: PlatformPay.IsGooglePaySupportedParams;
    }) => Promise<boolean>;
    /**
     * Launches the relevant native wallet sheet (Apple Pay on iOS, Google Pay on Android) in order to confirm a Stripe [SetupIntent](https://stripe.com/docs/api/setup_intents).
     * @param clientSecret The client secret of the SetupIntent.
     * @param params an object describing the Apple Pay and Google Pay configurations.
     * @returns An object with an error field if something went wrong or the flow was cancelled, otherwise an object with both `setupIntent` and `paymentMethod` fields.
     */
    confirmPlatformPaySetupIntent: (clientSecret: string, params: PlatformPay.ConfirmParams) => Promise<PlatformPay.ConfirmSetupIntentResult>;
    /**
     * Launches the relevant native wallet sheet (Apple Pay on iOS, Google Pay on Android) in order to confirm a Stripe [PaymentIntent](https://stripe.com/docs/api/payment_intents).
     * @param clientSecret The client secret of the PaymentIntent.
     * @param params an object describing the Apple Pay and Google Pay configurations.
     * @returns An object with an error field if something went wrong or the flow was cancelled, otherwise an object with both `paymentIntent` and `paymentMethod` fields.
     */
    confirmPlatformPayPayment: (clientSecret: string, params: PlatformPay.ConfirmParams) => Promise<PlatformPay.ConfirmPaymentResult>;
    /**
     * Launches the relevant native wallet sheet (Apple Pay on iOS, Google Pay on Android) in order to create a Stripe [PaymentMethod](https://stripe.com/docs/api/payment_methods) and [token](https://stripe.com/docs/api/tokens).
     * @param params an object describing the Apple Pay and Google Pay configurations.
     * @returns An object with an error field if something went wrong or the flow was cancelled, otherwise an object with both `paymentMethod` and `token` fields.
     */
    createPlatformPayPaymentMethod: (params: PlatformPay.PaymentMethodParams) => Promise<PlatformPay.PaymentMethodResult>;
    /**
     * @deprecated The Tokens API is deprecated, you should use Payment Methods and `createPlatformPayPaymentMethod` instead.  Launches the relevant native wallet sheet (Apple Pay on iOS, Google Pay on Android) in order to create a Stripe [token](https://stripe.com/docs/api/tokens).
     * @param params an object describing the Apple Pay and Google Pay configurations.
     * @returns An object with an error field if something went wrong or the flow was cancelled, otherwise an object with a `token` field.
     */
    createPlatformPayToken: (params: PlatformPay.PaymentMethodParams) => Promise<PlatformPay.TokenResult>;
    /**
     * Dismiss the Apple Pay sheet if it is open. iOS only, this is a no-op on Android.
     * @returns A boolean indicating whether or not the sheet was successfully closed. Will return false if the Apple Pay sheet was not open.
     */
    dismissPlatformPay: () => Promise<boolean>;
    /**
     * Update different items on the Apple Pay sheet, including the summary items, the shipping methods, and any errors shown. iOS only, this is a no-op on Android.
     * @param cartItems An array of payment summary items to display in the Apple Pay sheet.
     * @param shippingMethods An array of shipping methods to display in the Apple Pay sheet.
     * @param errors An array of errors associated with the user's input that must be corrected to proceed with payment. These errors will be shown in the Apple Pay sheet.
     *
     * @returns An object with an optional 'error' field, which is only populated if something went wrong.
     */
    updatePlatformPaySheet: (params: {
        applePay: {
            cartItems: Array<PlatformPay.CartSummaryItem>;
            shippingMethods: Array<PlatformPay.ShippingMethod>;
            errors: Array<PlatformPay.ApplePaySheetError>;
        };
    }) => Promise<{
        error?: import("../types").StripeError<import("../types").PlatformPayError>;
    }>;
    /**
     * Check if the app & device support adding this card to the native wallet.
     * @param params An object containing fields for `primaryAccountIdentifier`, `cardLastFour`, and `testEnv`.
     *
     * @returns A promise resolving to an object of type CanAddCardToWalletResult. Check the `canAddCard` field, if it's true, you should show the `<AddToWalletButton />`
     */
    canAddCardToWallet: (params: CanAddCardToWalletParams) => Promise<CanAddCardToWalletResult>;
    /**
     * iOS only, this is a no-op on Android. Use this method to move users to the interface for adding credit cards.
     * This method transfers control to the Wallet app on iPhone or to the Settings
     * app on iPad. For devices that don’t support Apple Pay, this method does nothing.
     */
    openPlatformPaySetup: () => Promise<void>;
};
//# sourceMappingURL=usePlatformPay.d.ts.map