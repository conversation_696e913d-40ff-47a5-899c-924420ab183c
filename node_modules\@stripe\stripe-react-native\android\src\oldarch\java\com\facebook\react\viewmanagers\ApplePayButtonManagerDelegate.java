/**
* This code was generated by [react-native-codegen](https://www.npmjs.com/package/react-native-codegen).
*
* Do not edit this file as changes may cause incorrect behavior and will be lost
* once the code is regenerated.
*
* @generated by codegen project: GeneratePropsJavaDelegate.js
*/

package com.facebook.react.viewmanagers;

import android.view.View;
import androidx.annotation.Nullable;
import com.facebook.react.uimanager.BaseViewManager;
import com.facebook.react.uimanager.BaseViewManagerDelegate;
import com.facebook.react.uimanager.LayoutShadowNode;

public class ApplePayButtonManagerDelegate<T extends View, U extends BaseViewManager<T, ? extends LayoutShadowNode> & ApplePayButtonManagerInterface<T>> extends BaseViewManagerDelegate<T, U> {
  public ApplePayButtonManagerDelegate(U viewManager) {
    super(viewManager);
  }
  @Override
  public void setProperty(T view, String propName, @Nullable Object value) {
    switch (propName) {
      case "disabled":
        mViewManager.setDisabled(view, value == null ? false : (boolean) value);
        break;
      case "type":
        mViewManager.setType(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "buttonStyle":
        mViewManager.setButtonStyle(view, value == null ? 0 : ((Double) value).intValue());
        break;
      case "borderRadius":
        mViewManager.setBorderRadius(view, value == null ? 4 : ((Double) value).intValue());
        break;
      default:
        super.setProperty(view, propName, value);
    }
  }
}
