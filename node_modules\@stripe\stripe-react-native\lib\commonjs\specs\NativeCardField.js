var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.default=exports.__INTERNAL_VIEW_CONFIG=exports.Commands=void 0;var _codegenNativeCommands=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeCommands"));var _codegenNativeComponent=_interopRequireDefault(require("react-native/Libraries/Utilities/codegenNativeComponent"));var NativeComponentRegistry=require('react-native/Libraries/NativeComponent/NativeComponentRegistry');var _require=require('react-native/Libraries/NativeComponent/ViewConfigIgnore'),ConditionallyIgnoredEventHandlers=_require.ConditionallyIgnoredEventHandlers;var _require2=require("react-native/Libraries/ReactNative/RendererProxy"),dispatchCommand=_require2.dispatchCommand;var nativeComponentName='CardField';var __INTERNAL_VIEW_CONFIG=exports.__INTERNAL_VIEW_CONFIG={uiViewClassName:'CardField',directEventTypes:{topCardChange:{registrationName:'onCardChange'},topFocusChange:{registrationName:'onFocusChange'}},validAttributes:Object.assign({autofocus:true,cardStyle:true,countryCode:true,dangerouslyGetFullCardDetails:true,disabled:true,onBehalfOf:true,placeholders:true,postalCodeEnabled:true,preferredNetworks:true},ConditionallyIgnoredEventHandlers({onCardChange:true,onFocusChange:true}))};var _default=exports.default=NativeComponentRegistry.get(nativeComponentName,function(){return __INTERNAL_VIEW_CONFIG;});var Commands=exports.Commands={blur:function blur(ref){dispatchCommand(ref,"blur",[]);},focus:function focus(ref){dispatchCommand(ref,"focus",[]);},clear:function clear(ref){dispatchCommand(ref,"clear",[]);}};
//# sourceMappingURL=NativeCardField.js.map