{"version": 3, "names": ["_reactNative", "require", "_NativeStripeSdkModule", "_interopRequireDefault", "_react", "_interopRequireWildcard", "_events", "_NativeEmbeddedPaymentElement", "_jsxRuntime", "_jsxFileName", "_getRequireWildcardCache", "e", "WeakMap", "r", "t", "__esModule", "default", "has", "get", "n", "__proto__", "a", "Object", "defineProperty", "getOwnPropertyDescriptor", "u", "hasOwnProperty", "call", "i", "set", "EmbeddedPaymentElement", "_classCallCheck2", "_createClass2", "key", "value", "_update", "_asyncToGenerator2", "intentConfig", "result", "NativeStripeSdkModule", "updateEmbeddedPaymentElement", "update", "_x", "apply", "arguments", "_confirm", "confirmEmbeddedPaymentElement", "confirm", "clearPaymentOption", "clearEmbeddedPaymentOption", "confirmHandlerCallback", "formSheetActionConfirmCallback", "createEmbeddedPaymentElement", "_x2", "_x3", "_createEmbeddedPaymentElement", "configuration", "setupConfirmHandlers", "_configuration$formSh", "<PERSON><PERSON><PERSON><PERSON>", "_confirmHandlerCallba", "remove", "addListener", "_ref", "paymentMethod", "shouldSavePaymentMethod", "intentCreationCallback", "formSheetAction", "type", "confirmFormSheetHandler", "onFormSheetConfirmComplete", "_formSheetActionConfi", "useEmbeddedPaymentElement", "_this", "isAndroid", "Platform", "OS", "elementRef", "useRef", "_useState", "useState", "_useState2", "_slicedToArray2", "element", "setElement", "_useState3", "_useState4", "paymentOption", "setPaymentOption", "_useState5", "_useState6", "height", "setHeight", "viewRef", "_useState7", "_useState8", "loadingError", "setLoadingError", "getElementOrThrow", "ref", "current", "Error", "useEffect", "active", "el", "getCurrentRef", "_elementRef$current", "currentRef", "Commands", "sub", "_ref3", "opt", "_ref4", "h", "LayoutAnimation", "configureNext", "Presets", "easeInEaseOut", "nativeError", "message", "embeddedPaymentElementView", "useMemo", "jsx", "style", "width", "intentConfiguration", "useCallback", "promise", "Promise", "resolve", "reject", "cfg", "tag", "findNodeHandle"], "sourceRoot": "../../../src", "sources": ["types/EmbeddedPaymentElement.tsx"], "mappings": "4jBACA,IAAAA,YAAA,CAAAC,OAAA,iBAeA,IAAAC,sBAAA,CAAAC,sBAAA,CAAAF,OAAA,oCACA,IAAAG,MAAA,CAAAC,uBAAA,CAAAJ,OAAA,WAUA,IAAAK,OAAA,CAAAL,OAAA,cACA,IAAAM,6BAAA,CAAAF,uBAAA,CAAAJ,OAAA,2CAG+C,IAAAO,WAAA,CAAAP,OAAA,0BAAAQ,YAAA,0FAAAC,yBAAAC,CAAA,wBAAAC,OAAA,iBAAAC,CAAA,KAAAD,OAAA,GAAAE,CAAA,KAAAF,OAAA,UAAAF,wBAAA,UAAAA,yBAAAC,CAAA,SAAAA,CAAA,CAAAG,CAAA,CAAAD,CAAA,IAAAF,CAAA,YAAAN,wBAAAM,CAAA,CAAAE,CAAA,MAAAA,CAAA,EAAAF,CAAA,EAAAA,CAAA,CAAAI,UAAA,QAAAJ,CAAA,WAAAA,CAAA,mBAAAA,CAAA,qBAAAA,CAAA,QAAAK,OAAA,CAAAL,CAAA,MAAAG,CAAA,CAAAJ,wBAAA,CAAAG,CAAA,KAAAC,CAAA,EAAAA,CAAA,CAAAG,GAAA,CAAAN,CAAA,SAAAG,CAAA,CAAAI,GAAA,CAAAP,CAAA,MAAAQ,CAAA,EAAAC,SAAA,OAAAC,CAAA,CAAAC,MAAA,CAAAC,cAAA,EAAAD,MAAA,CAAAE,wBAAA,SAAAC,CAAA,IAAAd,CAAA,gBAAAc,CAAA,KAAAC,cAAA,CAAAC,IAAA,CAAAhB,CAAA,CAAAc,CAAA,OAAAG,CAAA,CAAAP,CAAA,CAAAC,MAAA,CAAAE,wBAAA,CAAAb,CAAA,CAAAc,CAAA,OAAAG,CAAA,GAAAA,CAAA,CAAAV,GAAA,EAAAU,CAAA,CAAAC,GAAA,EAAAP,MAAA,CAAAC,cAAA,CAAAJ,CAAA,CAAAM,CAAA,CAAAG,CAAA,EAAAT,CAAA,CAAAM,CAAA,EAAAd,CAAA,CAAAc,CAAA,UAAAN,CAAA,CAAAH,OAAA,CAAAL,CAAA,CAAAG,CAAA,EAAAA,CAAA,CAAAe,GAAA,CAAAlB,CAAA,CAAAQ,CAAA,EAAAA,CAAA,KA+IzC,CAAAW,sBAAsB,qBAAAA,uBAAA,KAAAC,gBAAA,CAAAf,OAAA,OAAAc,sBAAA,YAAAE,aAAA,CAAAhB,OAAA,EAAAc,sBAAA,GAAAG,GAAA,UAAAC,KAAA,iBAAAC,OAAA,IAAAC,kBAAA,CAAApB,OAAA,EAO1B,UAAaqB,YAAmD,CAAE,CAChE,GAAM,CAAAC,MAAM,MACJ,CAAAC,8BAAqB,CAACC,4BAA4B,CAACH,YAAY,CAAC,CACxE,MAAO,CAAAC,MAAM,CACf,CAAC,UAJK,CAAAG,MAAMA,CAAAC,EAAA,SAAAP,OAAA,CAAAQ,KAAA,MAAAC,SAAA,SAAN,CAAAH,MAAM,QAAAR,GAAA,WAAAC,KAAA,iBAAAW,QAAA,IAAAT,kBAAA,CAAApB,OAAA,EAaZ,WAAuD,CACrD,GAAM,CAAAsB,MAAM,MACJ,CAAAC,8BAAqB,CAACO,6BAA6B,CAAC,CAAC,CAAC,CAAC,CAC/D,MAAO,CAAAR,MAAM,CACf,CAAC,UAJK,CAAAS,OAAOA,CAAA,SAAAF,QAAA,CAAAF,KAAA,MAAAC,SAAA,SAAP,CAAAG,OAAO,QAAAd,GAAA,sBAAAC,KAAA,CAOb,SAAAc,kBAAkBA,CAAA,CAAS,CACzBT,8BAAqB,CAACU,0BAA0B,CAAC,CAAC,CAAC,CAAC,CACtD,CAAC,QAMH,GAAI,CAAAC,sBAAgD,CAAG,IAAI,CAC3D,GAAI,CAAAC,8BAAwD,CAAG,IAAI,CAAC,QAErD,CAAAC,4BAA4BA,CAAAC,GAAA,CAAAC,GAAA,SAAAC,6BAAA,CAAAZ,KAAA,MAAAC,SAAA,YAAAW,8BAAA,EAAAA,6BAAA,IAAAnB,kBAAA,CAAApB,OAAA,EAA3C,UACEqB,YAAmD,CACnDmB,aAAkD,CACjB,CACjCC,oBAAoB,CAACpB,YAAY,CAAEmB,aAAa,CAAC,CAEjD,KAAM,CAAAjB,8BAAqB,CAACa,4BAA4B,CACtDf,YAAY,CACZmB,aACF,CAAC,CACD,MAAO,IAAI,CAAA1B,sBAAsB,CAAC,CAAC,CACrC,CAAC,SAAAyB,6BAAA,CAAAZ,KAAA,MAAAC,SAAA,GAED,QAAS,CAAAa,oBAAoBA,CAC3BpB,YAAmD,CACnDmB,aAAkD,CAClD,KAAAE,qBAAA,CACA,GAAM,CAAAC,cAAc,CAAGtB,YAAY,CAACsB,cAAc,CAClD,GAAIA,cAAc,CAAE,KAAAC,qBAAA,CAClB,CAAAA,qBAAA,CAAAV,sBAAsB,SAAtBU,qBAAA,CAAwBC,MAAM,CAAC,CAAC,CAChCX,sBAAsB,CAAG,GAAAY,mBAAW,EAClC,0BAA0B,CAC1B,SAAAC,IAAA,CAMM,IALJ,CAAAC,aAAa,CAAAD,IAAA,CAAbC,aAAa,CACbC,uBAAuB,CAAAF,IAAA,CAAvBE,uBAAuB,CAKvBN,cAAc,CACZK,aAAa,CACbC,uBAAuB,CACvB1B,8BAAqB,CAAC2B,sBACxB,CAAC,CACH,CACF,CAAC,CACH,CAEA,GAAI,EAAAR,qBAAA,CAAAF,aAAa,CAACW,eAAe,eAA7BT,qBAAA,CAA+BU,IAAI,IAAK,SAAS,CAAE,CACrD,GAAM,CAAAC,uBAAuB,CAC3Bb,aAAa,CAACW,eAAe,CAACG,0BAA0B,CAC1D,GAAID,uBAAuB,CAAE,KAAAE,qBAAA,CAC3B,CAAAA,qBAAA,CAAApB,8BAA8B,SAA9BoB,qBAAA,CAAgCV,MAAM,CAAC,CAAC,CACxCV,8BAA8B,CAAG,GAAAW,mBAAW,EAC1C,gDAAgD,CAChD,SAACxB,MAAoC,CAAK,CAExC+B,uBAAuB,CAAC/B,MAAM,CAAC,CACjC,CACF,CAAC,CACH,CACF,CACF,CA0CO,QAAS,CAAAkC,yBAAyBA,CACvCnC,YAAmD,CACnDmB,aAAkD,CACjB,KAAAiB,KAAA,MACjC,GAAM,CAAAC,SAAS,CAAGC,qBAAQ,CAACC,EAAE,GAAK,SAAS,CAC3C,GAAM,CAAAC,UAAU,CAAG,GAAAC,aAAM,EAAgC,IAAI,CAAC,CAC9D,IAAAC,SAAA,CAA8B,GAAAC,eAAQ,EAAgC,IAAI,CAAC,CAAAC,UAAA,IAAAC,eAAA,CAAAlE,OAAA,EAAA+D,SAAA,IAApEI,OAAO,CAAAF,UAAA,IAAEG,UAAU,CAAAH,UAAA,IAC1B,IAAAI,UAAA,CACE,GAAAL,eAAQ,EAAkC,IAAI,CAAC,CAAAM,UAAA,IAAAJ,eAAA,CAAAlE,OAAA,EAAAqE,UAAA,IAD1CE,aAAa,CAAAD,UAAA,IAAEE,gBAAgB,CAAAF,UAAA,IAEtC,IAAAG,UAAA,CAA4B,GAAAT,eAAQ,EAAqB,CAAC,CAAAU,UAAA,IAAAR,eAAA,CAAAlE,OAAA,EAAAyE,UAAA,IAAnDE,MAAM,CAAAD,UAAA,IAAEE,SAAS,CAAAF,UAAA,IACxB,GAAM,CAAAG,OAAO,CAAG,GAAAf,aAAM,EAAiD,IAAI,CAAC,CAC5E,IAAAgB,UAAA,CAAwC,GAAAd,eAAQ,EAAe,IAAI,CAAC,CAAAe,UAAA,IAAAb,eAAA,CAAAlE,OAAA,EAAA8E,UAAA,IAA7DE,YAAY,CAAAD,UAAA,IAAEE,eAAe,CAAAF,UAAA,IAEpC,QAAS,CAAAG,iBAAiBA,CAACC,GAE1B,CAA0B,CACzB,GAAI,CAACA,GAAG,CAACC,OAAO,CAAE,CAChB,KAAM,IAAI,CAAAC,KAAK,CACb,mGACF,CAAC,CACH,CACA,MAAO,CAAAF,GAAG,CAACC,OAAO,CACpB,CAGA,GAAAE,gBAAS,EAAC,UAAM,CACd,GAAI,CAAAC,MAAM,CAAG,IAAI,CACjB,GAAAnE,kBAAA,CAAApB,OAAA,EAAC,WAAY,CACX,GAAM,CAAAwF,EAAE,MAAS,CAAApD,4BAA4B,CAC3Cf,YAAY,CACZmB,aACF,CAAC,CACD,GAAI,CAAC+C,MAAM,CAAE,OACb1B,UAAU,CAACuB,OAAO,CAAGI,EAAE,CACvBpB,UAAU,CAACoB,EAAE,CAAC,CAChB,CAAC,EAAE,CAAC,CACJ,GAAM,CAAAC,aAAa,CAAG,QAAhB,CAAAA,aAAaA,CAAA,QAAS,CAAAZ,OAAO,CAACO,OAAO,GAE3C,MAAO,WAAM,KAAAM,mBAAA,CACXH,MAAM,CAAG,KAAK,CACd,CAAAG,mBAAA,CAAA7B,UAAU,CAACuB,OAAO,SAAlBM,mBAAA,CAAoB1D,kBAAkB,CAAC,CAAC,CACxC6B,UAAU,CAACuB,OAAO,CAAG,IAAI,CAEzB,GAAM,CAAAO,UAAU,CAAGF,aAAa,CAAC,CAAC,CAElC,GAAI/B,SAAS,EAAIiC,UAAU,CAAE,CAC3BC,sCAAQ,CAAC5D,kBAAkB,CAAC2D,UAAU,CAAC,CACzC,CAEAvB,UAAU,CAAC,IAAI,CAAC,CAClB,CAAC,CACH,CAAC,CAAE,CAAC/C,YAAY,CAAEmB,aAAa,CAAEqC,OAAO,CAAEnB,SAAS,CAAC,CAAC,CAErD,GAAA4B,gBAAS,EAAC,UAAM,CACd,GAAM,CAAAO,GAAG,CAAG,GAAA/C,mBAAW,EACrB,8CAA8C,CAC9C,SAAAgD,KAAA,KAAkB,CAAAC,GAAG,CAAAD,KAAA,CAAlBvB,aAAa,OAAY,CAAAC,gBAAgB,CAACuB,GAAG,OAAHA,GAAG,CAAI,IAAI,CAAC,EAC3D,CAAC,CACD,MAAO,kBAAM,CAAAF,GAAG,CAAChD,MAAM,CAAC,CAAC,GAC3B,CAAC,CAAC,CAGF,GAAAyC,gBAAS,EAAC,UAAM,CACd,GAAM,CAAAO,GAAG,CAAG,GAAA/C,mBAAW,EACrB,uCAAuC,CACvC,SAAAkD,KAAA,CAAmB,IAAR,CAAAC,CAAC,CAAAD,KAAA,CAATrB,MAAM,CAEP,GAAIsB,CAAC,CAAG,CAAC,EAAKvC,SAAS,EAAIuC,CAAC,GAAK,CAAE,CAAE,CACnCC,4BAAe,CAACC,aAAa,CAACD,4BAAe,CAACE,OAAO,CAACC,aAAa,CAAC,CACpEzB,SAAS,CAACqB,CAAC,CAAC,CACd,CACF,CACF,CAAC,CACD,MAAO,kBAAM,CAAAJ,GAAG,CAAChD,MAAM,CAAC,CAAC,GAC3B,CAAC,CAAE,CAACa,SAAS,CAAC,CAAC,CAGf,GAAA4B,gBAAS,EAAC,UAAM,CACd,GAAM,CAAAO,GAAG,CAAG,GAAA/C,mBAAW,EACrB,qCAAqC,CACrC,SAACwD,WAAgC,CAAK,CACpCrB,eAAe,CAAC,GAAI,CAAAI,KAAK,CAACiB,WAAW,CAACC,OAAO,CAAC,CAAC,CACjD,CACF,CAAC,CACD,MAAO,kBAAM,CAAAV,GAAG,CAAChD,MAAM,CAAC,CAAC,GAC3B,CAAC,CAAE,EAAE,CAAC,CAGN,GAAM,CAAA2D,0BAA0B,CAAG,GAAAC,cAAO,EAAC,UAAM,CAC/C,GAAI/C,SAAS,EAAIlB,aAAa,EAAInB,YAAY,CAAE,CAC9C,MACE,GAAA7B,WAAA,CAAAkH,GAAA,EAACnH,6BAAA,CAAAS,OAA4B,EAC3BmF,GAAG,CAAEN,OAAQ,CACb8B,KAAK,CAAE,CAAC,CAAEC,KAAK,CAAE,MAAM,CAAEjC,MAAM,CAAEA,MAAO,CAAC,CAAE,CAC3CnC,aAAa,CAAEA,aAAc,CAC7BqE,mBAAmB,CAAExF,YAAa,CACnC,CAAC,CAEN,CACA,GAAI,CAAC8C,OAAO,CAAE,MAAO,KAAI,CACzB,MACE,GAAA3E,WAAA,CAAAkH,GAAA,EAACnH,6BAAA,CAAAS,OAA4B,EAC3BmF,GAAG,CAAEN,OAAQ,CACb8B,KAAK,CAAE,CAAEC,KAAK,CAAE,MAAM,CAAEjC,MAAM,CAANA,MAAO,CAAE,CACjCnC,aAAa,CAAEA,aAAc,CAC7BqE,mBAAmB,CAAExF,YAAa,CACnC,CAAC,CAEN,CAAC,CAAE,CAACmB,aAAa,CAAE2B,OAAO,CAAEQ,MAAM,CAAEtD,YAAY,CAAEqC,SAAS,CAAC,CAAC,CAG7D,GAAM,CAAA3B,OAAO,CAAG,GAAA+E,kBAAW,EAAC,UAA6C,CACvE,GAAM,CAAAnB,UAAU,CAAGd,OAAO,CAACO,OAAO,CAElC,GAAI1B,SAAS,CAAE,CACb,GAAIiC,UAAU,CAAE,CACd,GAAM,CAAAoB,OAAO,CAAG,GAAI,CAAAC,OAAO,CAA+B,SAACC,OAAO,CAAK,CACrE,GAAM,CAAApB,GAAG,CAAG,GAAA/C,mBAAW,EACrB,gDAAgD,CAChD,SAACxB,MAAoC,CAAK,CACxCuE,GAAG,CAAChD,MAAM,CAAC,CAAC,CACZoE,OAAO,CAAC3F,MAAM,CAAC,CACjB,CACF,CAAC,CACH,CAAC,CAAC,CAEFsE,sCAAQ,CAAC7D,OAAO,CAAC4D,UAAU,CAAC,CAE5B,MAAO,CAAAoB,OAAO,CAChB,CAAC,IAAM,CACL,MAAO,CAAAC,OAAO,CAACE,MAAM,CACnB,GAAI,CAAA7B,KAAK,CAAC,uDAAuD,CACnE,CAAC,CACH,CACF,CAGA,MAAO,CAAAH,iBAAiB,CAACrB,UAAU,CAAC,CAAC9B,OAAO,CAAC,CAAC,CAChD,CAAC,CAAE,CAAC2B,SAAS,CAAC,CAAC,CACf,GAAM,CAAAjC,MAAM,CAAG,GAAAqF,kBAAW,EACxB,SAACK,GAA0C,QACzC,CAAAjC,iBAAiB,CAACrB,UAAU,CAAC,CAACpC,MAAM,CAAC0F,GAAG,CAAC,GAC3C,EACF,CAAC,CACD,GAAM,CAAAnF,kBAAkB,CAAG,GAAA8E,kBAAW,EAAC,UAAqB,CAC1D,GAAIpD,SAAS,CAAE,CACb,GAAM,CAAA0D,GAAG,CAAG,GAAAC,2BAAc,EAACxC,OAAO,CAACO,OAAO,CAAC,CAC3C,GAAIgC,GAAG,EAAI,IAAI,CAAE,CACf,MAAO,CAAAJ,OAAO,CAACE,MAAM,CAAC,GAAI,CAAA7B,KAAK,CAAC,oCAAoC,CAAC,CAAC,CACxE,CACA,MAAO,CAAA9D,8BAAqB,CAACU,0BAA0B,CAACmF,GAAG,CAAC,CAC9D,CAGAlC,iBAAiB,CAACrB,UAAU,CAAC,CAAC7B,kBAAkB,CAAC,CAAC,CAClD,MAAO,CAAAgF,OAAO,CAACC,OAAO,CAAC,CAAC,CAC1B,CAAC,CAAE,CAACvD,SAAS,CAAC,CAAC,CAEf,MAAO,CACL8C,0BAA0B,CAA1BA,0BAA0B,CAC1BjC,aAAa,CAAbA,aAAa,CACbxC,OAAO,CAAPA,OAAO,CACPN,MAAM,CAANA,MAAM,CACNO,kBAAkB,CAAlBA,kBAAkB,CAClBgD,YAAY,CAAZA,YACF,CAAC,CACH", "ignoreList": []}