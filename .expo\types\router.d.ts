/* eslint-disable */
import * as Router from 'expo-router';

export * from 'expo-router';

declare module 'expo-router' {
  export namespace ExpoRouter {
    export interface __routes<T extends string | object = string> {
      hrefInputParams: { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/achievements`; params?: Router.UnknownInputParams; } | { pathname: `/create-flashcard`; params?: Router.UnknownInputParams; } | { pathname: `/flashcard-review`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/offline-review`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/subscription`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/upload-course`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/courses` | `/courses`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/flashcards` | `/flashcards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/progress` | `/progress`; params?: Router.UnknownInputParams; } | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/course/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/edit-course/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
      hrefOutputParams: { pathname: Router.RelativePathString, params?: Router.UnknownOutputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownOutputParams } | { pathname: `/achievements`; params?: Router.UnknownOutputParams; } | { pathname: `/create-flashcard`; params?: Router.UnknownOutputParams; } | { pathname: `/flashcard-review`; params?: Router.UnknownOutputParams; } | { pathname: `/help`; params?: Router.UnknownOutputParams; } | { pathname: `/offline-review`; params?: Router.UnknownOutputParams; } | { pathname: `/privacy`; params?: Router.UnknownOutputParams; } | { pathname: `/settings`; params?: Router.UnknownOutputParams; } | { pathname: `/subscription`; params?: Router.UnknownOutputParams; } | { pathname: `/terms`; params?: Router.UnknownOutputParams; } | { pathname: `/upload-course`; params?: Router.UnknownOutputParams; } | { pathname: `/welcome`; params?: Router.UnknownOutputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/courses` | `/courses`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/flashcards` | `/flashcards`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownOutputParams; } | { pathname: `${'/(tabs)'}/progress` | `/progress`; params?: Router.UnknownOutputParams; } | { pathname: `/+not-found`, params: Router.UnknownOutputParams & {  } } | { pathname: `/course/[id]`, params: Router.UnknownOutputParams & { id: string; } } | { pathname: `/edit-course/[id]`, params: Router.UnknownOutputParams & { id: string; } };
      href: Router.RelativePathString | Router.ExternalPathString | `/achievements${`?${string}` | `#${string}` | ''}` | `/create-flashcard${`?${string}` | `#${string}` | ''}` | `/flashcard-review${`?${string}` | `#${string}` | ''}` | `/help${`?${string}` | `#${string}` | ''}` | `/offline-review${`?${string}` | `#${string}` | ''}` | `/privacy${`?${string}` | `#${string}` | ''}` | `/settings${`?${string}` | `#${string}` | ''}` | `/subscription${`?${string}` | `#${string}` | ''}` | `/terms${`?${string}` | `#${string}` | ''}` | `/upload-course${`?${string}` | `#${string}` | ''}` | `/welcome${`?${string}` | `#${string}` | ''}` | `/_sitemap${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-in${`?${string}` | `#${string}` | ''}` | `/sign-in${`?${string}` | `#${string}` | ''}` | `${'/(auth)'}/sign-up${`?${string}` | `#${string}` | ''}` | `/sign-up${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/courses${`?${string}` | `#${string}` | ''}` | `/courses${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/flashcards${`?${string}` | `#${string}` | ''}` | `/flashcards${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}${`?${string}` | `#${string}` | ''}` | `/${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/profile${`?${string}` | `#${string}` | ''}` | `/profile${`?${string}` | `#${string}` | ''}` | `${'/(tabs)'}/progress${`?${string}` | `#${string}` | ''}` | `/progress${`?${string}` | `#${string}` | ''}` | { pathname: Router.RelativePathString, params?: Router.UnknownInputParams } | { pathname: Router.ExternalPathString, params?: Router.UnknownInputParams } | { pathname: `/achievements`; params?: Router.UnknownInputParams; } | { pathname: `/create-flashcard`; params?: Router.UnknownInputParams; } | { pathname: `/flashcard-review`; params?: Router.UnknownInputParams; } | { pathname: `/help`; params?: Router.UnknownInputParams; } | { pathname: `/offline-review`; params?: Router.UnknownInputParams; } | { pathname: `/privacy`; params?: Router.UnknownInputParams; } | { pathname: `/settings`; params?: Router.UnknownInputParams; } | { pathname: `/subscription`; params?: Router.UnknownInputParams; } | { pathname: `/terms`; params?: Router.UnknownInputParams; } | { pathname: `/upload-course`; params?: Router.UnknownInputParams; } | { pathname: `/welcome`; params?: Router.UnknownInputParams; } | { pathname: `/_sitemap`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-in` | `/sign-in`; params?: Router.UnknownInputParams; } | { pathname: `${'/(auth)'}/sign-up` | `/sign-up`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/courses` | `/courses`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/flashcards` | `/flashcards`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}` | `/`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/profile` | `/profile`; params?: Router.UnknownInputParams; } | { pathname: `${'/(tabs)'}/progress` | `/progress`; params?: Router.UnknownInputParams; } | `/+not-found` | `/course/${Router.SingleRoutePart<T>}` | `/edit-course/${Router.SingleRoutePart<T>}` | { pathname: `/+not-found`, params: Router.UnknownInputParams & {  } } | { pathname: `/course/[id]`, params: Router.UnknownInputParams & { id: string | number; } } | { pathname: `/edit-course/[id]`, params: Router.UnknownInputParams & { id: string | number; } };
    }
  }
}
