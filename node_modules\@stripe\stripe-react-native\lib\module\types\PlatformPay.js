Object.defineProperty(exports,"__esModule",{value:true});exports.PaymentType=exports.PaymentRequestType=exports.InvalidShippingField=exports.IntervalUnit=exports.ContactField=exports.ButtonType=exports.ButtonStyle=exports.BillingAddressFormat=exports.ApplePayShippingType=exports.ApplePaySheetErrorType=exports.ApplePayMerchantCapability=void 0;var ApplePaySheetErrorType=exports.ApplePaySheetErrorType=function(ApplePaySheetErrorType){ApplePaySheetErrorType["InvalidShippingAddress"]="InvalidShippingAddress";ApplePaySheetErrorType["UnserviceableShippingAddress"]="UnserviceableShippingAddress";ApplePaySheetErrorType["InvalidCouponCode"]="InvalidCouponCode";ApplePaySheetErrorType["ExpiredCouponCode"]="ExpiredCouponCode";return ApplePaySheetErrorType;}({});var ContactField=exports.ContactField=function(ContactField){ContactField["EmailAddress"]="emailAddress";ContactField["Name"]="name";ContactField["PhoneNumber"]="phoneNumber";ContactField["PhoneticName"]="phoneticName";ContactField["PostalAddress"]="postalAddress";return ContactField;}({});var InvalidShippingField=exports.InvalidShippingField=function(InvalidShippingField){InvalidShippingField["Street"]="street";InvalidShippingField["City"]="city";InvalidShippingField["SubAdministrativeArea"]="subAdministrativeArea";InvalidShippingField["State"]="state";InvalidShippingField["PostalCode"]="postalCode";InvalidShippingField["Country"]="country";InvalidShippingField["CountryCode"]="countryCode";InvalidShippingField["SubLocality"]="subLocality";return InvalidShippingField;}({});var PaymentRequestType=exports.PaymentRequestType=function(PaymentRequestType){PaymentRequestType["Recurring"]="Recurring";PaymentRequestType["AutomaticReload"]="AutomaticReload";PaymentRequestType["MultiMerchant"]="MultiMerchant";return PaymentRequestType;}({});var ApplePayMerchantCapability=exports.ApplePayMerchantCapability=function(ApplePayMerchantCapability){ApplePayMerchantCapability["Supports3DS"]="supports3DS";ApplePayMerchantCapability["SupportsCredit"]="supportsCredit";ApplePayMerchantCapability["SupportsDebit"]="supportsDebit";return ApplePayMerchantCapability;}({});var ApplePayShippingType=exports.ApplePayShippingType=function(ApplePayShippingType){ApplePayShippingType["Shipping"]="shipping";ApplePayShippingType["Delivery"]="delivery";ApplePayShippingType["StorePickup"]="storePickup";ApplePayShippingType["ServicePickup"]="servicePickup";return ApplePayShippingType;}({});var BillingAddressFormat=exports.BillingAddressFormat=function(BillingAddressFormat){BillingAddressFormat["Full"]="FULL";BillingAddressFormat["Min"]="MIN";return BillingAddressFormat;}({});var ButtonType=exports.ButtonType=function(ButtonType){ButtonType[ButtonType["Default"]=0]="Default";ButtonType[ButtonType["Buy"]=1]="Buy";ButtonType[ButtonType["Book"]=6]="Book";ButtonType[ButtonType["Checkout"]=5]="Checkout";ButtonType[ButtonType["Donate"]=4]="Donate";ButtonType[ButtonType["Order"]=11]="Order";ButtonType[ButtonType["Subscribe"]=7]="Subscribe";ButtonType[ButtonType["SetUp"]=2]="SetUp";ButtonType[ButtonType["InStore"]=3]="InStore";ButtonType[ButtonType["Reload"]=8]="Reload";ButtonType[ButtonType["AddMoney"]=9]="AddMoney";ButtonType[ButtonType["TopUp"]=10]="TopUp";ButtonType[ButtonType["Rent"]=12]="Rent";ButtonType[ButtonType["Support"]=13]="Support";ButtonType[ButtonType["Contribute"]=14]="Contribute";ButtonType[ButtonType["Tip"]=15]="Tip";ButtonType[ButtonType["Continue"]=16]="Continue";ButtonType[ButtonType["Pay"]=1000]="Pay";ButtonType[ButtonType["GooglePayMark"]=1001]="GooglePayMark";return ButtonType;}({});var ButtonStyle=exports.ButtonStyle=function(ButtonStyle){ButtonStyle[ButtonStyle["White"]=0]="White";ButtonStyle[ButtonStyle["WhiteOutline"]=1]="WhiteOutline";ButtonStyle[ButtonStyle["Black"]=2]="Black";ButtonStyle[ButtonStyle["Automatic"]=3]="Automatic";return ButtonStyle;}({});var PaymentType=exports.PaymentType=function(PaymentType){PaymentType["Deferred"]="Deferred";PaymentType["Immediate"]="Immediate";PaymentType["Recurring"]="Recurring";return PaymentType;}({});var IntervalUnit=exports.IntervalUnit=function(IntervalUnit){IntervalUnit["Minute"]="minute";IntervalUnit["Hour"]="hour";IntervalUnit["Day"]="day";IntervalUnit["Month"]="month";IntervalUnit["Year"]="year";return IntervalUnit;}({});
//# sourceMappingURL=PlatformPay.js.map