var _interopRequireDefault=require("@babel/runtime/helpers/interopRequireDefault");Object.defineProperty(exports,"__esModule",{value:true});exports.AddressSheet=AddressSheet;var _objectWithoutProperties2=_interopRequireDefault(require("@babel/runtime/helpers/objectWithoutProperties"));var _react=_interopRequireDefault(require("react"));var _NativeAddressSheet=_interopRequireDefault(require("../specs/NativeAddressSheet"));var _jsxRuntime=require("react/jsx-runtime");var _jsxFileName="/Users/<USER>/stripe/stripe-react-native/src/components/AddressSheet.tsx";var _excluded=["onSubmit","onError"];function AddressSheet(_ref){var onSubmit=_ref.onSubmit,onError=_ref.onError,props=(0,_objectWithoutProperties2.default)(_ref,_excluded);return(0,_jsxRuntime.jsx)(_NativeAddressSheet.default,Object.assign({},props,{onSubmitAction:function onSubmitAction(event){return onSubmit(event.nativeEvent.result);},onErrorAction:function onErrorAction(event){return onError(event.nativeEvent.error);}}));}
//# sourceMappingURL=AddressSheet.js.map